page 50115 "Plot Contact Card"
{
    Caption = 'Plot Contact Card';
    PageType = Card;
    SourceTable = "Plot Contacts";

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Contact Code"; Rec."Contact Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact code.';
                }
                field("Contact Name"; Rec."Contact Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact name.';
                }
                field("Contact Type"; Rec."Contact Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact type.';
                }
                field("Job Title"; Rec."Job Title")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the job title.';
                }
                field("Company Name"; Rec."Company Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the company name.';
                }
                field("Primary Contact"; Rec."Primary Contact")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if this is the primary contact.';
                }
                field("Active"; Rec."Active")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if the contact is active.';
                }
            }
            group("Hierarchy Information")
            {
                Caption = 'Hierarchy Information';
                field("Site Code"; Rec."Site Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site code.';
                }
                field("Phase Code"; Rec."Phase Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phase code.';
                }
                field("Plot Code"; Rec."Plot Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot code.';
                }
                field("Contact Hierarchy Level"; Rec."Contact Hierarchy Level")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact hierarchy level.';
                }
            }
            group("Contact Information")
            {
                Caption = 'Contact Information';
                field("Phone No."; Rec."Phone No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phone number.';
                }
                field("Mobile No."; Rec."Mobile No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the mobile number.';
                }
                field("E-Mail"; Rec."E-Mail")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the email address.';
                }
                field("Preferred Contact Method"; Rec."Preferred Contact Method")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the preferred contact method.';
                }
                field("Available Hours"; Rec."Available Hours")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies available hours.';
                }
            }
            group("Address Information")
            {
                Caption = 'Address Information';
                field("Address"; Rec."Address")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the address.';
                }
                field("Address 2"; Rec."Address 2")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies additional address information.';
                }
                field("City"; Rec."City")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the city.';
                }
                field("Post Code"; Rec."Post Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the post code.';
                }
                field("County"; Rec."County")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the county.';
                }
                field("Country/Region Code"; Rec."Country/Region Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the country/region code.';
                }
            }
            group("Business Central Integration")
            {
                Caption = 'Business Central Integration';
                field("BC Contact No."; Rec."BC Contact No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the linked Business Central contact number.';
                }
            }
            group("Additional Information")
            {
                Caption = 'Additional Information';
                field("Notes"; Rec."Notes")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies additional notes.';
                    MultiLine = true;
                }
            }
        }
        area(factboxes)
        {
            part("Contact Details"; "Contact Details FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "No." = field("BC Contact No.");
                Visible = Rec."BC Contact No." <> '';
            }
            part("Plot Information"; "Plot Information FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "Plot Code" = field("Plot Code");
                Visible = Rec."Plot Code" <> '';
            }
        }
    }

    actions
    {
        area(processing)
        {
            action("Set as Primary")
            {
                ApplicationArea = All;
                Caption = 'Set as Primary';
                Image = Default;
                ToolTip = 'Set this contact as the primary contact.';
                
                trigger OnAction()
                begin
                    Rec."Primary Contact" := true;
                    Rec.Modify();
                    Message('Contact %1 set as primary.', Rec."Contact Name");
                end;
            }
            action("Create Communication Log")
            {
                ApplicationArea = All;
                Caption = 'Create Communication Log';
                Image = Log;
                ToolTip = 'Create a new communication log entry.';
                
                trigger OnAction()
                var
                    CommunicationLog: Record "Communication Log";
                    CommunicationLogCard: Page "Communication Log Card";
                begin
                    CommunicationLog.Init();
                    CommunicationLog."Site Code" := Rec."Site Code";
                    CommunicationLog."Phase Code" := Rec."Phase Code";
                    CommunicationLog."Plot Code" := Rec."Plot Code";
                    CommunicationLog."Contact Code" := Rec."Contact Code";
                    CommunicationLog.Insert(true);
                    
                    CommunicationLogCard.SetRecord(CommunicationLog);
                    CommunicationLogCard.Run();
                end;
            }
            action("Link to BC Contact")
            {
                ApplicationArea = All;
                Caption = 'Link to BC Contact';
                Image = Link;
                ToolTip = 'Link this contact to a Business Central contact.';
                
                trigger OnAction()
                var
                    Contact: Record Contact;
                    ContactList: Page "Contact List";
                begin
                    ContactList.LookupMode(true);
                    if ContactList.RunModal() = Action::LookupOK then begin
                        ContactList.GetRecord(Contact);
                        Rec."BC Contact No." := Contact."No.";
                        Rec.Modify();
                        CurrPage.Update();
                        Message('Contact linked to BC Contact %1.', Contact."No.");
                    end;
                end;
            }
        }
        area(navigation)
        {
            action("Communication Log")
            {
                ApplicationArea = All;
                Caption = 'Communication Log';
                Image = Log;
                ToolTip = 'View communication log for this contact.';
                
                trigger OnAction()
                var
                    CommunicationLog: Record "Communication Log";
                    CommunicationLogList: Page "Communication Log List";
                begin
                    CommunicationLog.SetRange("Contact Code", Rec."Contact Code");
                    CommunicationLogList.SetTableView(CommunicationLog);
                    CommunicationLogList.Run();
                end;
            }
        }
    }
}
