page 50104 "Plot List"
{
    ApplicationArea = All;
    Caption = 'Plot List';
    PageType = List;
    SourceTable = "Plot Master";
    UsageCategory = Lists;
    CardPageId = "Plot Card";
    Editable = false;

    layout
    {
        area(content)
        {
            repeater(General)
            {
                field("Plot Code"; Rec."Plot Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the unique code for the plot.';
                }
                field("Plot Number"; Rec."Plot Number")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot number.';
                }
                field("Site Code"; Rec."Site Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site code for this plot.';
                }
                field("Site Name"; Rec."Site Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site name for this plot.';
                }
                field("Phase Code"; Rec."Phase Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phase code for this plot.';
                }
                field("Phase Name"; Rec."Phase Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phase name for this plot.';
                }
                field("Customer No."; Rec."Customer No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer number.';
                }
                field("Customer Name"; Rec."Customer Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer name.';
                }
                field("House Type Code"; Rec."House Type Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the house type code for this plot.';
                }
                field("House Type Name"; Rec."House Type Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the house type name for this plot.';
                }
                field("Status"; Rec."Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the current status of the plot.';
                }
                field("Plot Address"; Rec."Plot Address")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the address of the plot.';
                }
                field("City"; Rec."City")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the city where the plot is located.';
                }
                field("Post Code"; Rec."Post Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the postal code for the plot.';
                }
                field("Contact Name"; Rec."Contact Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact name for this plot.';
                }
                field("Contact Phone"; Rec."Contact Phone")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact phone number for this plot.';
                }
                field("Expected Start Date"; Rec."Expected Start Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected start date for the plot.';
                }
                field("Expected Completion Date"; Rec."Expected Completion Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected completion date for the plot.';
                }
                field("Sequence No."; Rec."Sequence No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the sequence number for plot ordering.';
                }
            }
        }
        area(factboxes)
        {
            systempart(Control1900383207; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(Control1905767507; Notes)
            {
                ApplicationArea = Notes;
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("Plot")
            {
                Caption = 'Plot';
                action("Phase")
                {
                    ApplicationArea = All;
                    Caption = 'Phase';
                    Image = Planning;
                    RunObject = Page "Phase Card";
                    RunPageLink = "Phase Code" = field("Phase Code"), "Site Code" = field("Site Code");
                    ToolTip = 'View the phase card for this plot.';
                }
                action("Site")
                {
                    ApplicationArea = All;
                    Caption = 'Site';
                    Image = Home;
                    RunObject = Page "Site Card";
                    RunPageLink = "Site Code" = field("Site Code");
                    ToolTip = 'View the site card for this plot.';
                }
                action("House Type Card")
                {
                    ApplicationArea = All;
                    Caption = 'House Type';
                    Image = Item;
                    RunObject = Page "House Type Card";
                    RunPageLink = "House Type Code" = field("House Type Code");
                    ToolTip = 'View the house type card for this plot.';
                }
            }
        }
        area(processing)
        {
            action("New Plot")
            {
                ApplicationArea = All;
                Caption = 'New Plot';
                Image = New;
                RunObject = Page "Plot Card";
                RunPageMode = Create;
                ToolTip = 'Create a new plot.';
            }
        }
        area(reporting)
        {
            action("Plot Summary")
            {
                ApplicationArea = All;
                Caption = 'Plot Summary';
                Image = Report;
                ToolTip = 'Print a summary report for the selected plot.';

                trigger OnAction()
                begin
                    Message('Plot Summary report will be implemented in Phase 8.');
                end;
            }
        }
    }
}
