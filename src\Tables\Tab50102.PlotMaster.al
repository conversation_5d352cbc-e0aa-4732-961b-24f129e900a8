table 50102 "Plot Master"
{
    Caption = 'Plot Master';
    DataClassification = CustomerContent;
    LookupPageId = "Plot List";
    DrillDownPageId = "Plot List";

    fields
    {
        field(1; "Plot Code"; Code[20])
        {
            Caption = 'Plot Code';
            NotBlank = true;
            DataClassification = CustomerContent;
        }
        field(2; "Site Code"; Code[20])
        {
            Caption = 'Site Code';
            TableRelation = "Site Master"."Site Code";
            NotBlank = true;
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                SiteMaster: Record "Site Master";
            begin
                if "Site Code" <> '' then begin
                    SiteMaster.Get("Site Code");
                    "Site Name" := SiteMaster."Site Name";
                    "Customer No." := SiteMaster."Customer No.";
                    "Customer Name" := SiteMaster."Customer Name";
                end else begin
                    "Site Name" := '';
                    "Customer No." := '';
                    "Customer Name" := '';
                end;
            end;
        }
        field(3; "Phase Code"; Code[20])
        {
            Caption = 'Phase Code';
            TableRelation = "Phase Master"."Phase Code" where("Site Code" = field("Site Code"));
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                PhaseMaster: Record "Phase Master";
            begin
                if ("Phase Code" <> '') and ("Site Code" <> '') then begin
                    PhaseMaster.Get("Phase Code", "Site Code");
                    "Phase Name" := PhaseMaster."Phase Name";
                end else
                    "Phase Name" := '';
            end;
        }
        field(4; "Site Name"; Text[100])
        {
            Caption = 'Site Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(5; "Phase Name"; Text[100])
        {
            Caption = 'Phase Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(6; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(7; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(8; "Plot Address"; Text[100])
        {
            Caption = 'Plot Address';
            DataClassification = CustomerContent;
        }
        field(9; "Plot Address 2"; Text[50])
        {
            Caption = 'Plot Address 2';
            DataClassification = CustomerContent;
        }
        field(10; "City"; Text[30])
        {
            Caption = 'City';
            DataClassification = CustomerContent;
        }
        field(11; "Post Code"; Code[20])
        {
            Caption = 'Post Code';
            DataClassification = CustomerContent;
        }
        field(12; "County"; Text[30])
        {
            Caption = 'County';
            DataClassification = CustomerContent;
        }
        field(13; "Country/Region Code"; Code[10])
        {
            Caption = 'Country/Region Code';
            TableRelation = "Country/Region";
            DataClassification = CustomerContent;
        }
        field(14; "Contact Name"; Text[50])
        {
            Caption = 'Contact Name';
            DataClassification = CustomerContent;
        }
        field(15; "Contact Phone"; Text[30])
        {
            Caption = 'Contact Phone';
            DataClassification = CustomerContent;
        }
        field(16; "Contact Email"; Text[80])
        {
            Caption = 'Contact Email';
            DataClassification = CustomerContent;
        }
        field(17; "Expected Start Date"; Date)
        {
            Caption = 'Expected Start Date';
            DataClassification = CustomerContent;
        }
        field(18; "Expected Completion Date"; Date)
        {
            Caption = 'Expected Completion Date';
            DataClassification = CustomerContent;

            trigger OnValidate()
            begin
                if ("Expected Start Date" <> 0D) and ("Expected Completion Date" <> 0D) then
                    if "Expected Completion Date" < "Expected Start Date" then
                        Error('Expected Completion Date cannot be earlier than Expected Start Date.');
            end;
        }
        field(19; "Actual Start Date"; Date)
        {
            Caption = 'Actual Start Date';
            DataClassification = CustomerContent;
        }
        field(20; "Actual Completion Date"; Date)
        {
            Caption = 'Actual Completion Date';
            DataClassification = CustomerContent;

            trigger OnValidate()
            begin
                if ("Actual Start Date" <> 0D) and ("Actual Completion Date" <> 0D) then
                    if "Actual Completion Date" < "Actual Start Date" then
                        Error('Actual Completion Date cannot be earlier than Actual Start Date.');
            end;
        }
        field(21; "Phase/Block Reference"; Text[50])
        {
            Caption = 'Phase/Block Reference';
            DataClassification = CustomerContent;
        }
        field(22; "Access Instructions"; Text[250])
        {
            Caption = 'Access Instructions';
            DataClassification = CustomerContent;
        }
        field(23; "Special Requirements"; Text[250])
        {
            Caption = 'Special Requirements';
            DataClassification = CustomerContent;
        }
        field(24; "Status"; Enum "Plot Status")
        {
            Caption = 'Status';
            DataClassification = CustomerContent;
        }
        field(25; "House Type Code"; Code[20])
        {
            Caption = 'House Type Code';
            TableRelation = "House Type Master"."House Type Code";
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                HouseTypeMaster: Record "House Type Master";
            begin
                if "House Type Code" <> '' then begin
                    HouseTypeMaster.Get("House Type Code");
                    "House Type Name" := HouseTypeMaster."House Type Name";
                end else
                    "House Type Name" := '';
            end;
        }
        field(26; "House Type Name"; Text[100])
        {
            Caption = 'House Type Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(27; "Plot Number"; Text[20])
        {
            Caption = 'Plot Number';
            DataClassification = CustomerContent;
        }
        field(28; "Sequence No."; Integer)
        {
            Caption = 'Sequence No.';
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; "Plot Code")
        {
            Clustered = true;
        }
        key(Site; "Site Code", "Phase Code", "Sequence No.")
        {
        }
        key(Customer; "Customer No.", "Site Code", "Phase Code", "Plot Code")
        {
        }
        key(HouseType; "House Type Code", "Plot Code")
        {
        }
        key(Status; Status, "Site Code", "Phase Code")
        {
        }
    }

    trigger OnInsert()
    var
        PlotManagement: Codeunit "Plot Management";
    begin
        PlotManagement.ValidatePlotData(Rec);
    end;

    trigger OnModify()
    var
        PlotManagement: Codeunit "Plot Management";
    begin
        PlotManagement.ValidatePlotData(Rec);
        PlotManagement.UpdatePlotStatus(Rec);
    end;

    trigger OnDelete()
    begin
        // Add any cleanup logic here for future phases
    end;
}
