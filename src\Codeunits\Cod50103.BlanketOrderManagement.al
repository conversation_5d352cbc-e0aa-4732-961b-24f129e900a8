codeunit 50103 "Blanket Order Management"
{
    /// <summary>
    /// Validates plot-based blanket order data and ensures business rules are followed
    /// </summary>
    procedure ValidatePlotBasedOrder(var SalesHeader: Record "Sales Header")
    var
        SiteMaster: Record "Site Master";
        PlotMaster: Record "Plot Master";
    begin
        if not SalesHeader."Plot-Based Order" then
            exit;

        // Validate document type
        if SalesHeader."Document Type" <> SalesHeader."Document Type"::"Blanket Order" then
            Error('Plot-based orders are only supported for Blanket Orders.');

        // Validate site exists and is active
        if SalesHeader."Site Code" = '' then
            Error('Site Code must be specified for plot-based orders.');

        if not SiteMaster.Get(SalesHeader."Site Code") then
            Error('Site %1 does not exist.', SalesHeader."Site Code");

        if SiteMaster.Status = SiteMaster.Status::Cancelled then
            Error('Cannot create plot-based order for cancelled site %1.', SalesHeader."Site Code");

        // Validate customer matches site
        if SalesHeader."Sell-to Customer No." <> SiteMaster."Customer No." then
            Error('Customer %1 does not match site customer %2.',
                SalesHeader."Sell-to Customer No.", SiteMaster."Customer No.");
    end;

    /// <summary>
    /// Allocates sales lines to specific plots
    /// </summary>
    procedure AllocateLinesToPlots(DocumentType: Enum "Sales Document Type"; DocumentNo: Code[20])
    var
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        BlanketOrderPlotLines: Record "Blanket Order Plot Lines";
        PlotMaster: Record "Plot Master";
        LineNo: Integer;
    begin
        SalesHeader.Get(DocumentType, DocumentNo);
        if not SalesHeader."Plot-Based Order" then
            Error('Document %1 is not a plot-based order.', DocumentNo);

        // Get all plots for the site
        PlotMaster.SetRange("Site Code", SalesHeader."Site Code");
        PlotMaster.SetRange(Status, PlotMaster.Status::Active, PlotMaster.Status::Planning);

        if not PlotMaster.FindSet() then
            Error('No active plots found for site %1.', SalesHeader."Site Code");

        LineNo := 10000;
        repeat
            // Create plot line entry
            if not BlanketOrderPlotLines.Get(DocumentType, DocumentNo, LineNo) then begin
                BlanketOrderPlotLines.Init();
                BlanketOrderPlotLines."Document Type" := DocumentType;
                BlanketOrderPlotLines."Document No." := DocumentNo;
                BlanketOrderPlotLines."Line No." := LineNo;
                BlanketOrderPlotLines."Plot Code" := PlotMaster."Plot Code";
                BlanketOrderPlotLines."Allocation Status" := BlanketOrderPlotLines."Allocation Status"::"Not Allocated";
                BlanketOrderPlotLines.Insert(true);
                LineNo += 10000;
            end;
        until PlotMaster.Next() = 0;

        Message('Plot allocation completed. %1 plots allocated.', (LineNo - 10000) / 10000);
    end;

    /// <summary>
    /// Updates plot allocation status based on sales line quantities
    /// </summary>
    procedure UpdatePlotAllocationStatus(var BlanketOrderPlotLines: Record "Blanket Order Plot Lines")
    var
        SalesLine: Record "Sales Line";
        TotalAllocated: Decimal;
        TotalAmount: Decimal;
    begin
        // Calculate totals from sales lines
        SalesLine.SetRange("Document Type", BlanketOrderPlotLines."Document Type");
        SalesLine.SetRange("Document No.", BlanketOrderPlotLines."Document No.");
        SalesLine.SetRange("Plot Code", BlanketOrderPlotLines."Plot Code");

        if SalesLine.FindSet() then
            repeat
                TotalAllocated += SalesLine.Quantity;
                TotalAmount += SalesLine."Line Amount";
            until SalesLine.Next() = 0;

        // Update allocation status
        if TotalAllocated = 0 then
            BlanketOrderPlotLines."Allocation Status" := BlanketOrderPlotLines."Allocation Status"::"Not Allocated"
        else if TotalAllocated > 0 then
            BlanketOrderPlotLines."Allocation Status" := BlanketOrderPlotLines."Allocation Status"::"Partially Allocated";

        BlanketOrderPlotLines.Modify();
    end;

    /// <summary>
    /// Creates delivery locations for a plot
    /// </summary>
    procedure CreateDefaultDeliveryLocations(PlotCode: Code[20])
    var
        PlotMaster: Record "Plot Master";
        DeliveryLocations: Record "Delivery Locations";
        LocationCode: Code[20];
    begin
        PlotMaster.Get(PlotCode);

        // Create primary delivery location at plot address
        LocationCode := CopyStr(PlotCode + '-M', 1, 20);
        if not DeliveryLocations.Get(LocationCode, PlotCode) then begin
            DeliveryLocations.Init();
            DeliveryLocations."Location Code" := LocationCode;
            DeliveryLocations."Plot Code" := PlotCode;
            DeliveryLocations."Location Name" := 'Plot ' + PlotMaster."Plot Number" + ' - Main Delivery';
            DeliveryLocations."Location Type" := DeliveryLocations."Location Type"::"Plot Address";
            DeliveryLocations."Address" := PlotMaster."Plot Address";
            DeliveryLocations."City" := PlotMaster."City";
            DeliveryLocations."Post Code" := PlotMaster."Post Code";
            DeliveryLocations."Country/Region Code" := PlotMaster."Country/Region Code";
            DeliveryLocations."Primary Location" := true;
            DeliveryLocations."Active" := true;
            DeliveryLocations.Insert(true);
        end;

        // Create site office delivery location
        LocationCode := CopyStr(PlotCode + '-O', 1, 20);
        if not DeliveryLocations.Get(LocationCode, PlotCode) then begin
            DeliveryLocations.Init();
            DeliveryLocations."Location Code" := LocationCode;
            DeliveryLocations."Plot Code" := PlotCode;
            DeliveryLocations."Location Name" := 'Site Office - ' + PlotMaster."Site Code";
            DeliveryLocations."Location Type" := DeliveryLocations."Location Type"::"Site Office";
            DeliveryLocations."Primary Location" := false;
            DeliveryLocations."Active" := true;
            DeliveryLocations.Insert(true);
        end;
    end;

    /// <summary>
    /// Validates sales line plot allocation
    /// </summary>
    procedure ValidatePlotLineAllocation(var SalesLine: Record "Sales Line")
    var
        SalesHeader: Record "Sales Header";
        PlotMaster: Record "Plot Master";
        BlanketOrderPlotLines: Record "Blanket Order Plot Lines";
    begin
        if SalesLine."Plot Code" = '' then
            exit;

        // Get header to check if plot-based
        SalesHeader.Get(SalesLine."Document Type", SalesLine."Document No.");
        if not SalesHeader."Plot-Based Order" then
            exit;

        // Validate plot exists and belongs to the site
        if not PlotMaster.Get(SalesLine."Plot Code") then
            Error('Plot %1 does not exist.', SalesLine."Plot Code");

        if PlotMaster."Site Code" <> SalesHeader."Site Code" then
            Error('Plot %1 does not belong to site %2.', SalesLine."Plot Code", SalesHeader."Site Code");

        // Validate plot is in correct status
        if PlotMaster.Status = PlotMaster.Status::Cancelled then
            Error('Cannot allocate lines to cancelled plot %1.', SalesLine."Plot Code");

        // Update plot allocation status
        BlanketOrderPlotLines.SetRange("Document Type", SalesLine."Document Type");
        BlanketOrderPlotLines.SetRange("Document No.", SalesLine."Document No.");
        BlanketOrderPlotLines.SetRange("Plot Code", SalesLine."Plot Code");
        if BlanketOrderPlotLines.FindFirst() then
            UpdatePlotAllocationStatus(BlanketOrderPlotLines);
    end;

    /// <summary>
    /// Gets plot statistics for a blanket order
    /// </summary>
    procedure GetBlanketOrderPlotStatistics(DocumentType: Enum "Sales Document Type"; DocumentNo: Code[20]; var TotalPlots: Integer; var AllocatedPlots: Integer; var TotalAmount: Decimal)
    var
        BlanketOrderPlotLines: Record "Blanket Order Plot Lines";
    begin
        TotalPlots := 0;
        AllocatedPlots := 0;
        TotalAmount := 0;

        BlanketOrderPlotLines.SetRange("Document Type", DocumentType);
        BlanketOrderPlotLines.SetRange("Document No.", DocumentNo);
        if BlanketOrderPlotLines.FindSet() then
            repeat
                TotalPlots += 1;
                if BlanketOrderPlotLines."Allocation Status" <> BlanketOrderPlotLines."Allocation Status"::"Not Allocated" then
                    AllocatedPlots += 1;

                BlanketOrderPlotLines.CalcFields("Total Allocated Amount");
                TotalAmount += BlanketOrderPlotLines."Total Allocated Amount";
            until BlanketOrderPlotLines.Next() = 0;
    end;

    /// <summary>
    /// Creates communication log entry
    /// </summary>
    procedure LogCommunication(SiteCode: Code[20]; PhaseCode: Code[20]; PlotCode: Code[20]; ContactCode: Code[20]; CommunicationType: Enum "Plot Communication Type"; Subject: Text[100]; Description: Text[250])
    var
        CommunicationLog: Record "Communication Log";
    begin
        CommunicationLog.Init();
        CommunicationLog."Site Code" := SiteCode;
        CommunicationLog."Phase Code" := PhaseCode;
        CommunicationLog."Plot Code" := PlotCode;
        CommunicationLog."Contact Code" := ContactCode;
        CommunicationLog."Communication Type" := CommunicationType;
        CommunicationLog."Subject" := Subject;
        CommunicationLog."Description" := Description;
        CommunicationLog."Priority" := CommunicationLog."Priority"::Normal;
        CommunicationLog."Status" := CommunicationLog."Status"::Open;
        CommunicationLog."Direction" := CommunicationLog."Direction"::Internal;
        CommunicationLog.Insert(true);
    end;
}
