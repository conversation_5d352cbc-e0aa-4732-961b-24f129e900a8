page 50102 "Phase List"
{
    ApplicationArea = All;
    Caption = 'Phase List';
    PageType = List;
    SourceTable = "Phase Master";
    UsageCategory = Lists;
    CardPageId = "Phase Card";
    Editable = false;

    layout
    {
        area(content)
        {
            repeater(General)
            {
                field("Phase Code"; Rec."Phase Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the unique code for the phase.';
                }
                field("Phase Name"; Rec."Phase Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the name of the phase.';
                }
                field("Site Code"; Rec."Site Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site code for this phase.';
                }
                field("Site Name"; Rec."Site Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site name for this phase.';
                }
                field("Customer No."; Rec."Customer No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer number.';
                }
                field("Customer Name"; Rec."Customer Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer name.';
                }
                field("Status"; Rec."Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the current status of the phase.';
                }
                field("Expected Start Date"; Rec."Expected Start Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected start date for the phase.';
                }
                field("Expected Completion Date"; Rec."Expected Completion Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected completion date for the phase.';
                }
                field("Sequence No."; Rec."Sequence No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the sequence number for phase ordering.';
                }
                field("Total Plots"; Rec."Total Plots")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the total number of plots in this phase.';
                }
                field("Active Plots"; Rec."Active Plots")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the number of active plots in this phase.';
                }
            }
        }
        area(factboxes)
        {
            systempart(Control1900383207; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(Control1905767507; Notes)
            {
                ApplicationArea = Notes;
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("Phase")
            {
                Caption = 'Phase';
                action("Plots")
                {
                    ApplicationArea = All;
                    Caption = 'Plots';
                    Image = ItemLedger;
                    RunObject = Page "Plot List";
                    RunPageLink = "Site Code" = field("Site Code"), "Phase Code" = field("Phase Code");
                    ToolTip = 'View or edit plots for this phase.';
                }
                action("Site")
                {
                    ApplicationArea = All;
                    Caption = 'Site';
                    Image = Home;
                    RunObject = Page "Site Card";
                    RunPageLink = "Site Code" = field("Site Code");
                    ToolTip = 'View the site card for this phase.';
                }
            }
        }
        area(processing)
        {
            action("New Phase")
            {
                ApplicationArea = All;
                Caption = 'New Phase';
                Image = New;
                RunObject = Page "Phase Card";
                RunPageMode = Create;
                ToolTip = 'Create a new phase.';
            }
        }
        area(reporting)
        {
            action("Phase Summary")
            {
                ApplicationArea = All;
                Caption = 'Phase Summary';
                Image = Report;
                ToolTip = 'Print a summary report for the selected phase.';
                
                trigger OnAction()
                begin
                    Message('Phase Summary report will be implemented in Phase 8.');
                end;
            }
        }
    }
}
