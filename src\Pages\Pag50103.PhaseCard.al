page 50103 "Phase Card"
{
    ApplicationArea = All;
    Caption = 'Phase Card';
    PageType = Card;
    SourceTable = "Phase Master";

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Phase Code"; Rec."Phase Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the unique code for the phase.';
                }
                field("Phase Name"; Rec."Phase Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the name of the phase.';
                }
                field("Site Code"; Rec."Site Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site code for this phase.';
                }
                field("Site Name"; Rec."Site Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site name for this phase.';
                }
                field("Customer No."; Rec."Customer No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer number.';
                }
                field("Customer Name"; Rec."Customer Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer name.';
                }
                field("Status"; Rec."Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the current status of the phase.';
                }
            }
            group("Description")
            {
                Caption = 'Description';
                field("Phase Description"; Rec."Phase Description")
                {
                    ApplicationArea = All;
                    MultiLine = true;
                    ToolTip = 'Specifies a description of the phase.';
                }
            }
            group("Dates")
            {
                Caption = 'Dates';
                group("Expected")
                {
                    Caption = 'Expected';
                    field("Expected Start Date"; Rec."Expected Start Date")
                    {
                        ApplicationArea = All;
                        ToolTip = 'Specifies the expected start date for the phase.';
                    }
                    field("Expected Completion Date"; Rec."Expected Completion Date")
                    {
                        ApplicationArea = All;
                        ToolTip = 'Specifies the expected completion date for the phase.';
                    }
                }
                group("Actual")
                {
                    Caption = 'Actual';
                    field("Actual Start Date"; Rec."Actual Start Date")
                    {
                        ApplicationArea = All;
                        ToolTip = 'Specifies the actual start date for the phase.';
                    }
                    field("Actual Completion Date"; Rec."Actual Completion Date")
                    {
                        ApplicationArea = All;
                        ToolTip = 'Specifies the actual completion date for the phase.';
                    }
                }
            }
            group("Contact Information")
            {
                Caption = 'Contact Information';
                field("Phase Manager Name"; Rec."Phase Manager Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the name of the phase manager.';
                }
                field("Phase Manager Phone"; Rec."Phase Manager Phone")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phone number of the phase manager.';
                }
                field("Phase Manager Email"; Rec."Phase Manager Email")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the email address of the phase manager.';
                }
            }
            group("Sequencing")
            {
                Caption = 'Sequencing';
                field("Sequence No."; Rec."Sequence No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the sequence number for phase ordering.';
                }
                field("Predecessor Phase"; Rec."Predecessor Phase")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the predecessor phase that must be completed first.';
                }
            }
            group("Additional Information")
            {
                Caption = 'Additional Information';
                field("Special Requirements"; Rec."Special Requirements")
                {
                    ApplicationArea = All;
                    MultiLine = true;
                    ToolTip = 'Specifies any special requirements for the phase.';
                }
            }
            group("Statistics")
            {
                Caption = 'Statistics';
                field("Total Plots"; Rec."Total Plots")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the total number of plots in this phase.';
                }
                field("Active Plots"; Rec."Active Plots")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the number of active plots in this phase.';
                }
            }
        }
        area(factboxes)
        {
            systempart(Control1900383207; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(Control1905767507; Notes)
            {
                ApplicationArea = Notes;
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("Phase")
            {
                Caption = 'Phase';
                action("Plots")
                {
                    ApplicationArea = All;
                    Caption = 'Plots';
                    Image = ItemLedger;
                    RunObject = Page "Plot List";
                    RunPageLink = "Site Code" = field("Site Code"), "Phase Code" = field("Phase Code");
                    ToolTip = 'View or edit plots for this phase.';
                }
                action("Site")
                {
                    ApplicationArea = All;
                    Caption = 'Site';
                    Image = Home;
                    RunObject = Page "Site Card";
                    RunPageLink = "Site Code" = field("Site Code");
                    ToolTip = 'View the site card for this phase.';
                }
            }
        }
        area(processing)
        {
            action("Create Plot")
            {
                ApplicationArea = All;
                Caption = 'Create Plot';
                Image = NewDocument;
                ToolTip = 'Create a new plot for this phase.';
                
                trigger OnAction()
                var
                    PlotMaster: Record "Plot Master";
                    PlotCard: Page "Plot Card";
                begin
                    PlotMaster.Init();
                    PlotMaster."Site Code" := Rec."Site Code";
                    PlotMaster."Phase Code" := Rec."Phase Code";
                    PlotMaster.Insert(true);
                    PlotCard.SetRecord(PlotMaster);
                    PlotCard.Run();
                end;
            }
        }
    }
}
