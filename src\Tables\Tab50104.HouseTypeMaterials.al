table 50104 "House Type Materials"
{
    Caption = 'House Type Materials';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "House Type Code"; Code[20])
        {
            Caption = 'House Type Code';
            TableRelation = "House Type Master"."House Type Code";
            NotBlank = true;
            DataClassification = CustomerContent;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            DataClassification = CustomerContent;
        }
        field(3; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item."No.";
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if "Item No." <> '' then begin
                    Item.Get("Item No.");
                    Description := Item.Description;
                    "Description 2" := Item."Description 2";
                    "Unit of Measure Code" := Item."Base Unit of Measure";
                    "Unit Cost" := Item."Unit Cost";
                    CalcTotalCost();
                end else begin
                    Description := '';
                    "Description 2" := '';
                    "Unit of Measure Code" := '';
                    "Unit Cost" := 0;
                    "Total Cost" := 0;
                end;
            end;
        }
        field(4; "Description"; Text[100])
        {
            Caption = 'Description';
            DataClassification = CustomerContent;
        }
        field(5; "Description 2"; Text[50])
        {
            Caption = 'Description 2';
            DataClassification = CustomerContent;
        }
        field(6; "Quantity"; Decimal)
        {
            Caption = 'Quantity';
            DecimalPlaces = 0 : 5;
            DataClassification = CustomerContent;

            trigger OnValidate()
            begin
                CalcTotalCost();
            end;
        }
        field(7; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            TableRelation = "Unit of Measure";
            DataClassification = CustomerContent;
        }
        field(8; "Unit Cost"; Decimal)
        {
            Caption = 'Unit Cost';
            DecimalPlaces = 2 : 5;
            DataClassification = CustomerContent;

            trigger OnValidate()
            begin
                CalcTotalCost();
            end;
        }
        field(9; "Total Cost"; Decimal)
        {
            Caption = 'Total Cost';
            DecimalPlaces = 2 : 2;
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(10; "Material Category"; Text[50])
        {
            Caption = 'Material Category';
            DataClassification = CustomerContent;
        }
        field(11; "Installation Phase"; Text[50])
        {
            Caption = 'Installation Phase';
            DataClassification = CustomerContent;
        }
        field(12; "Critical Item"; Boolean)
        {
            Caption = 'Critical Item';
            DataClassification = CustomerContent;
        }
        field(13; "Lead Time (Days)"; Integer)
        {
            Caption = 'Lead Time (Days)';
            DataClassification = CustomerContent;
        }
        field(14; "Supplier No."; Code[20])
        {
            Caption = 'Supplier No.';
            TableRelation = Vendor."No.";
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                Vendor: Record Vendor;
            begin
                if "Supplier No." <> '' then begin
                    Vendor.Get("Supplier No.");
                    "Supplier Name" := Vendor.Name;
                end else
                    "Supplier Name" := '';
            end;
        }
        field(15; "Supplier Name"; Text[100])
        {
            Caption = 'Supplier Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(16; "Notes"; Text[250])
        {
            Caption = 'Notes';
            DataClassification = CustomerContent;
        }
        field(17; "Sequence No."; Integer)
        {
            Caption = 'Sequence No.';
            DataClassification = CustomerContent;
        }
        field(18; "Alternative Item 1"; Code[20])
        {
            Caption = 'Alternative Item 1';
            TableRelation = Item."No.";
            DataClassification = CustomerContent;
        }
        field(19; "Alternative Item 2"; Code[20])
        {
            Caption = 'Alternative Item 2';
            TableRelation = Item."No.";
            DataClassification = CustomerContent;
        }
        field(20; "Waste Factor %"; Decimal)
        {
            Caption = 'Waste Factor %';
            DecimalPlaces = 2 : 2;
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; "House Type Code", "Line No.")
        {
            Clustered = true;
        }
        key(Item; "Item No.", "House Type Code")
        {
        }
        key(Category; "Material Category", "House Type Code", "Line No.")
        {
        }
        key(Phase; "Installation Phase", "Sequence No.")
        {
        }
        key(Critical; "Critical Item", "House Type Code", "Line No.")
        {
        }
    }

    trigger OnInsert()
    var
        HouseTypeManagement: Codeunit "House Type Management";
    begin
        HouseTypeManagement.ValidateMaterialData(Rec);
    end;

    trigger OnModify()
    var
        HouseTypeManagement: Codeunit "House Type Management";
    begin
        HouseTypeManagement.ValidateMaterialData(Rec);
    end;

    local procedure CalcTotalCost()
    begin
        "Total Cost" := Quantity * "Unit Cost";
    end;
}
