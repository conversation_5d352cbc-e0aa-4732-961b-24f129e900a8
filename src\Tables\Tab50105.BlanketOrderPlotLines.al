table 50105 "Blanket Order Plot Lines"
{
    Caption = 'Blanket Order Plot Lines';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Document Type"; Enum "Sales Document Type")
        {
            Caption = 'Document Type';
            DataClassification = CustomerContent;
        }
        field(2; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            TableRelation = "Sales Header"."No." where("Document Type" = field("Document Type"));
            DataClassification = CustomerContent;
        }
        field(3; "Line No."; Integer)
        {
            Caption = 'Line No.';
            DataClassification = CustomerContent;
        }
        field(4; "Plot Code"; Code[20])
        {
            Caption = 'Plot Code';
            TableRelation = "Plot Master"."Plot Code";
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                PlotMaster: Record "Plot Master";
                SalesHeader: Record "Sales Header";
            begin
                if "Plot Code" <> '' then begin
                    PlotMaster.Get("Plot Code");
                    "Plot Name" := PlotMaster."Plot Number";
                    "Site Code" := PlotMaster."Site Code";
                    "Phase Code" := PlotMaster."Phase Code";
                    "House Type Code" := PlotMaster."House Type Code";
                    "Plot Status" := PlotMaster.Status;
                    
                    // Validate against document header
                    if SalesHeader.Get("Document Type", "Document No.") then begin
                        if SalesHeader."Site Code" <> PlotMaster."Site Code" then
                            Error('Plot %1 belongs to site %2, but document is for site %3.', 
                                "Plot Code", PlotMaster."Site Code", SalesHeader."Site Code");
                    end;
                end else begin
                    "Plot Name" := '';
                    "Site Code" := '';
                    "Phase Code" := '';
                    "House Type Code" := '';
                end;
            end;
        }
        field(5; "Plot Name"; Text[50])
        {
            Caption = 'Plot Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(6; "Site Code"; Code[20])
        {
            Caption = 'Site Code';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(7; "Phase Code"; Code[20])
        {
            Caption = 'Phase Code';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(8; "House Type Code"; Code[20])
        {
            Caption = 'House Type Code';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(9; "Plot Status"; Enum "Plot Status")
        {
            Caption = 'Plot Status';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(10; "Allocation Status"; Enum "Plot Allocation Status")
        {
            Caption = 'Allocation Status';
            DataClassification = CustomerContent;
        }
        field(11; "Total Allocated Lines"; Integer)
        {
            Caption = 'Total Allocated Lines';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Sales Line" where("Document Type" = field("Document Type"), "Document No." = field("Document No."), "Plot Code" = field("Plot Code")));
        }
        field(12; "Total Allocated Amount"; Decimal)
        {
            Caption = 'Total Allocated Amount';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Sales Line"."Line Amount" where("Document Type" = field("Document Type"), "Document No." = field("Document No."), "Plot Code" = field("Plot Code")));
        }
        field(13; "Primary Contact Code"; Code[20])
        {
            Caption = 'Primary Contact Code';
            TableRelation = "Plot Contacts"."Contact Code" where("Site Code" = field("Site Code"), "Plot Code" = field("Plot Code"));
            DataClassification = CustomerContent;
        }
        field(14; "Expected Start Date"; Date)
        {
            Caption = 'Expected Start Date';
            DataClassification = CustomerContent;
        }
        field(15; "Expected Completion Date"; Date)
        {
            Caption = 'Expected Completion Date';
            DataClassification = CustomerContent;

            trigger OnValidate()
            begin
                if ("Expected Start Date" <> 0D) and ("Expected Completion Date" <> 0D) then
                    if "Expected Completion Date" < "Expected Start Date" then
                        Error('Expected Completion Date cannot be earlier than Expected Start Date.');
            end;
        }
        field(16; "Actual Start Date"; Date)
        {
            Caption = 'Actual Start Date';
            DataClassification = CustomerContent;
        }
        field(17; "Actual Completion Date"; Date)
        {
            Caption = 'Actual Completion Date';
            DataClassification = CustomerContent;
        }
        field(18; "Delivery Instructions"; Text[250])
        {
            Caption = 'Delivery Instructions';
            DataClassification = CustomerContent;
        }
        field(19; "Special Requirements"; Text[250])
        {
            Caption = 'Special Requirements';
            DataClassification = CustomerContent;
        }
        field(20; "Created Date"; Date)
        {
            Caption = 'Created Date';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(21; "Created By"; Code[50])
        {
            Caption = 'Created By';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(22; "Last Modified Date"; Date)
        {
            Caption = 'Last Modified Date';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(23; "Last Modified By"; Code[50])
        {
            Caption = 'Last Modified By';
            Editable = false;
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; "Document Type", "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(Plot; "Plot Code", "Document Type", "Document No.")
        {
        }
        key(Site; "Site Code", "Phase Code", "Plot Code")
        {
        }
        key(Status; "Allocation Status", "Plot Status")
        {
        }
    }

    trigger OnInsert()
    begin
        "Created Date" := Today;
        "Created By" := UserId;
        "Last Modified Date" := Today;
        "Last Modified By" := UserId;
    end;

    trigger OnModify()
    begin
        "Last Modified Date" := Today;
        "Last Modified By" := UserId;
    end;
}
