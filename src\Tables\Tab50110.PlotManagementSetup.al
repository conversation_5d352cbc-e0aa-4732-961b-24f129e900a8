table 50110 "Plot Management Setup"
{
    Caption = 'Plot Management Setup';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            DataClassification = CustomerContent;
        }
        field(2; "Plot Blanket Order Nos."; Code[20])
        {
            Caption = 'Plot Blanket Order Nos.';
            TableRelation = "No. Series".Code;
            DataClassification = CustomerContent;
        }
        field(3; "Site Nos."; Code[20])
        {
            Caption = 'Site Nos.';
            TableRelation = "No. Series".Code;
            DataClassification = CustomerContent;
        }
        field(4; "Phase Nos."; Code[20])
        {
            Caption = 'Phase Nos.';
            TableRelation = "No. Series".Code;
            DataClassification = CustomerContent;
        }
        field(5; "Plot Nos."; Code[20])
        {
            Caption = 'Plot Nos.';
            TableRelation = "No. Series".Code;
            DataClassification = CustomerContent;
        }
        field(6; "House Type Nos."; Code[20])
        {
            Caption = 'House Type Nos.';
            TableRelation = "No. Series".Code;
            DataClassification = CustomerContent;
        }
        field(7; "Default Plot Status"; Enum "Plot Status")
        {
            Caption = 'Default Plot Status';
            DataClassification = CustomerContent;
        }
        field(8; "Default Site Status"; Enum "Site Status")
        {
            Caption = 'Default Site Status';
            DataClassification = CustomerContent;
        }
        field(9; "Default Phase Status"; Enum "Phase Status")
        {
            Caption = 'Default Phase Status';
            DataClassification = CustomerContent;
        }
        field(10; "Auto-Create Delivery Locations"; Boolean)
        {
            Caption = 'Auto-Create Delivery Locations';
            DataClassification = CustomerContent;
        }
        field(11; "Auto-Create Plot Contacts"; Boolean)
        {
            Caption = 'Auto-Create Plot Contacts';
            DataClassification = CustomerContent;
        }
        field(12; "Enable Communication Logging"; Boolean)
        {
            Caption = 'Enable Communication Logging';
            DataClassification = CustomerContent;
        }
        field(13; "Default Contact Type"; Enum "Plot Contact Type")
        {
            Caption = 'Default Contact Type';
            DataClassification = CustomerContent;
        }
        field(14; "Default Delivery Location Type"; Enum "Delivery Location Type")
        {
            Caption = 'Default Delivery Location Type';
            DataClassification = CustomerContent;
        }
        field(15; "Require Site Code on Orders"; Boolean)
        {
            Caption = 'Require Site Code on Orders';
            DataClassification = CustomerContent;
        }
        field(16; "Enable Multi-Location Delivery"; Boolean)
        {
            Caption = 'Enable Multi-Location Delivery';
            DataClassification = CustomerContent;
        }
        field(17; "Default Currency Code"; Code[10])
        {
            Caption = 'Default Currency Code';
            TableRelation = Currency.Code;
            DataClassification = CustomerContent;
        }
        field(18; "Plot Code Format"; Text[50])
        {
            Caption = 'Plot Code Format';
            DataClassification = CustomerContent;
        }
        field(19; "Phase Code Format"; Text[50])
        {
            Caption = 'Phase Code Format';
            DataClassification = CustomerContent;
        }
        field(20; "Site Code Format"; Text[50])
        {
            Caption = 'Site Code Format';
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    begin
        "Primary Key" := '';
        "Default Plot Status" := "Default Plot Status"::Active;
        "Default Site Status" := "Default Site Status"::Active;
        "Default Phase Status" := "Default Phase Status"::Active;
        "Auto-Create Delivery Locations" := true;
        "Enable Communication Logging" := true;
        "Default Contact Type" := "Default Contact Type"::"Site Manager";
        "Default Delivery Location Type" := "Default Delivery Location Type"::"Plot Address";
        "Require Site Code on Orders" := true;
        "Enable Multi-Location Delivery" := true;
    end;
}
