table 50106 "Delivery Locations"
{
    Caption = 'Delivery Locations';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Location Code"; Code[20])
        {
            Caption = 'Location Code';
            DataClassification = CustomerContent;
        }
        field(2; "Plot Code"; Code[20])
        {
            Caption = 'Plot Code';
            TableRelation = "Plot Master"."Plot Code";
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                PlotMaster: Record "Plot Master";
            begin
                if "Plot Code" <> '' then begin
                    PlotMaster.Get("Plot Code");
                    "Site Code" := PlotMaster."Site Code";
                    "Phase Code" := PlotMaster."Phase Code";
                    
                    // Set default location name if not specified
                    if "Location Name" = '' then
                        "Location Name" := 'Plot ' + PlotMaster."Plot Number" + ' - Main Delivery';
                end else begin
                    "Site Code" := '';
                    "Phase Code" := '';
                end;
            end;
        }
        field(3; "Site Code"; Code[20])
        {
            Caption = 'Site Code';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(4; "Phase Code"; Code[20])
        {
            Caption = 'Phase Code';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(5; "Location Name"; Text[100])
        {
            Caption = 'Location Name';
            DataClassification = CustomerContent;
        }
        field(6; "Location Type"; Enum "Delivery Location Type")
        {
            Caption = 'Location Type';
            DataClassification = CustomerContent;
        }
        field(7; "Address"; Text[100])
        {
            Caption = 'Address';
            DataClassification = CustomerContent;
        }
        field(8; "Address 2"; Text[50])
        {
            Caption = 'Address 2';
            DataClassification = CustomerContent;
        }
        field(9; "City"; Text[30])
        {
            Caption = 'City';
            DataClassification = CustomerContent;
        }
        field(10; "Post Code"; Code[20])
        {
            Caption = 'Post Code';
            DataClassification = CustomerContent;
        }
        field(11; "County"; Text[30])
        {
            Caption = 'County';
            DataClassification = CustomerContent;
        }
        field(12; "Country/Region Code"; Code[10])
        {
            Caption = 'Country/Region Code';
            TableRelation = "Country/Region";
            DataClassification = CustomerContent;
        }
        field(13; "Contact Name"; Text[100])
        {
            Caption = 'Contact Name';
            DataClassification = CustomerContent;
        }
        field(14; "Phone No."; Text[30])
        {
            Caption = 'Phone No.';
            DataClassification = CustomerContent;
        }
        field(15; "E-Mail"; Text[80])
        {
            Caption = 'E-Mail';
            DataClassification = CustomerContent;
        }
        field(16; "Access Instructions"; Text[250])
        {
            Caption = 'Access Instructions';
            DataClassification = CustomerContent;
        }
        field(17; "Delivery Hours"; Text[100])
        {
            Caption = 'Delivery Hours';
            DataClassification = CustomerContent;
        }
        field(18; "Special Requirements"; Text[250])
        {
            Caption = 'Special Requirements';
            DataClassification = CustomerContent;
        }
        field(19; "GPS Coordinates"; Text[50])
        {
            Caption = 'GPS Coordinates';
            DataClassification = CustomerContent;
        }
        field(20; "Primary Location"; Boolean)
        {
            Caption = 'Primary Location';
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                DeliveryLocations: Record "Delivery Locations";
            begin
                if "Primary Location" then begin
                    // Ensure only one primary location per plot
                    DeliveryLocations.SetRange("Plot Code", "Plot Code");
                    DeliveryLocations.SetFilter("Location Code", '<>%1', "Location Code");
                    DeliveryLocations.SetRange("Primary Location", true);
                    if DeliveryLocations.FindSet() then
                        repeat
                            DeliveryLocations."Primary Location" := false;
                            DeliveryLocations.Modify();
                        until DeliveryLocations.Next() = 0;
                end;
            end;
        }
        field(21; "Active"; Boolean)
        {
            Caption = 'Active';
            DataClassification = CustomerContent;
        }
        field(22; "Created Date"; Date)
        {
            Caption = 'Created Date';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(23; "Created By"; Code[50])
        {
            Caption = 'Created By';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(24; "Last Modified Date"; Date)
        {
            Caption = 'Last Modified Date';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(25; "Last Modified By"; Code[50])
        {
            Caption = 'Last Modified By';
            Editable = false;
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; "Location Code", "Plot Code")
        {
            Clustered = true;
        }
        key(Plot; "Plot Code", "Primary Location")
        {
        }
        key(Site; "Site Code", "Phase Code", "Plot Code")
        {
        }
        key(Type; "Location Type", "Active")
        {
        }
    }

    trigger OnInsert()
    begin
        "Created Date" := Today;
        "Created By" := UserId;
        "Last Modified Date" := Today;
        "Last Modified By" := UserId;
        "Active" := true;
    end;

    trigger OnModify()
    begin
        "Last Modified Date" := Today;
        "Last Modified By" := UserId;
    end;
}
