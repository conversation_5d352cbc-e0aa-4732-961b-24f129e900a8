table 50108 "Communication Log"
{
    Caption = 'Communication Log';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            AutoIncrement = true;
            DataClassification = CustomerContent;
        }
        field(2; "Communication Date"; Date)
        {
            Caption = 'Communication Date';
            DataClassification = CustomerContent;
        }
        field(3; "Communication Time"; Time)
        {
            Caption = 'Communication Time';
            DataClassification = CustomerContent;
        }
        field(4; "Site Code"; Code[20])
        {
            Caption = 'Site Code';
            TableRelation = "Site Master"."Site Code";
            DataClassification = CustomerContent;
        }
        field(5; "Phase Code"; Code[20])
        {
            Caption = 'Phase Code';
            TableRelation = "Phase Master"."Phase Code" where("Site Code" = field("Site Code"));
            DataClassification = CustomerContent;
        }
        field(6; "Plot Code"; Code[20])
        {
            Caption = 'Plot Code';
            TableRelation = "Plot Master"."Plot Code" where("Site Code" = field("Site Code"), "Phase Code" = field("Phase Code"));
            DataClassification = CustomerContent;
        }
        field(7; "Contact Code"; Code[20])
        {
            Caption = 'Contact Code';
            TableRelation = "Plot Contacts"."Contact Code" where("Site Code" = field("Site Code"), "Phase Code" = field("Phase Code"), "Plot Code" = field("Plot Code"));
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                PlotContacts: Record "Plot Contacts";
            begin
                if "Contact Code" <> '' then begin
                    PlotContacts.SetRange("Contact Code", "Contact Code");
                    PlotContacts.SetRange("Site Code", "Site Code");
                    PlotContacts.SetRange("Phase Code", "Phase Code");
                    PlotContacts.SetRange("Plot Code", "Plot Code");
                    if PlotContacts.FindFirst() then begin
                        "Contact Name" := PlotContacts."Contact Name";
                        "Contact Type" := PlotContacts."Contact Type";
                    end;
                end else begin
                    "Contact Name" := '';
                end;
            end;
        }
        field(8; "Contact Name"; Text[100])
        {
            Caption = 'Contact Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(9; "Contact Type"; Enum "Plot Contact Type")
        {
            Caption = 'Contact Type';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(10; "Communication Type"; Enum "Plot Communication Type")
        {
            Caption = 'Communication Type';
            DataClassification = CustomerContent;
        }
        field(11; "Communication Method"; Enum "Preferred Contact Method")
        {
            Caption = 'Communication Method';
            DataClassification = CustomerContent;
        }
        field(12; "Direction"; Enum "Communication Direction")
        {
            Caption = 'Direction';
            DataClassification = CustomerContent;
        }
        field(13; "Subject"; Text[100])
        {
            Caption = 'Subject';
            DataClassification = CustomerContent;
        }
        field(14; "Description"; Text[250])
        {
            Caption = 'Description';
            DataClassification = CustomerContent;
        }
        field(15; "Priority"; Enum "Communication Priority")
        {
            Caption = 'Priority';
            DataClassification = CustomerContent;
        }
        field(16; "Status"; Enum "Communication Status")
        {
            Caption = 'Status';
            DataClassification = CustomerContent;
        }
        field(17; "Follow-up Required"; Boolean)
        {
            Caption = 'Follow-up Required';
            DataClassification = CustomerContent;
        }
        field(18; "Follow-up Date"; Date)
        {
            Caption = 'Follow-up Date';
            DataClassification = CustomerContent;
        }
        field(19; "Follow-up Completed"; Boolean)
        {
            Caption = 'Follow-up Completed';
            DataClassification = CustomerContent;
        }
        field(20; "Document Type"; Enum "Sales Document Type")
        {
            Caption = 'Related Document Type';
            DataClassification = CustomerContent;
        }
        field(21; "Document No."; Code[20])
        {
            Caption = 'Related Document No.';
            DataClassification = CustomerContent;
        }
        field(22; "Document Line No."; Integer)
        {
            Caption = 'Related Document Line No.';
            DataClassification = CustomerContent;
        }
        field(23; "Attachment"; Boolean)
        {
            Caption = 'Attachment';
            DataClassification = CustomerContent;
        }
        field(24; "Attachment Path"; Text[250])
        {
            Caption = 'Attachment Path';
            DataClassification = CustomerContent;
        }
        field(25; "Created By"; Code[50])
        {
            Caption = 'Created By';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(26; "Created Date"; Date)
        {
            Caption = 'Created Date';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(27; "Last Modified By"; Code[50])
        {
            Caption = 'Last Modified By';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(28; "Last Modified Date"; Date)
        {
            Caption = 'Last Modified Date';
            Editable = false;
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
        key(Site; "Site Code", "Communication Date", "Communication Time")
        {
        }
        key(Phase; "Site Code", "Phase Code", "Communication Date", "Communication Time")
        {
        }
        key(Plot; "Site Code", "Phase Code", "Plot Code", "Communication Date", "Communication Time")
        {
        }
        key(Contact; "Contact Code", "Communication Date", "Communication Time")
        {
        }
        key(Document; "Document Type", "Document No.", "Document Line No.")
        {
        }
        key(FollowUp; "Follow-up Required", "Follow-up Date", "Follow-up Completed")
        {
        }
        key(Priority; "Priority", "Status", "Communication Date")
        {
        }
    }

    trigger OnInsert()
    begin
        "Created By" := UserId;
        "Created Date" := Today;
        "Last Modified By" := UserId;
        "Last Modified Date" := Today;

        if "Communication Date" = 0D then
            "Communication Date" := Today;
        if "Communication Time" = 0T then
            "Communication Time" := Time;
    end;

    trigger OnModify()
    begin
        "Last Modified By" := UserId;
        "Last Modified Date" := Today;
    end;
}
