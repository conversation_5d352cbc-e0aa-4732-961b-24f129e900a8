tableextension 50100 "Sales Header Extension" extends "Sales Header"
{
    fields
    {
        field(50100; "Site Code"; Code[20])
        {
            Caption = 'Site Code';
            TableRelation = "Site Master"."Site Code";
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                SiteMaster: Record "Site Master";
            begin
                if "Site Code" <> '' then begin
                    SiteMaster.Get("Site Code");
                    "Site Name" := SiteMaster."Site Name";
                    
                    // Validate customer matches
                    if ("Sell-to Customer No." <> '') and ("Sell-to Customer No." <> SiteMaster."Customer No.") then
                        Error('Site %1 belongs to customer %2, but order is for customer %3.', 
                            "Site Code", SiteMaster."Customer No.", "Sell-to Customer No.");
                end else
                    "Site Name" := '';
            end;
        }
        field(50101; "Site Name"; Text[100])
        {
            Caption = 'Site Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50102; "Plot-Based Order"; Boolean)
        {
            Caption = 'Plot-Based Order';
            DataClassification = CustomerContent;

            trigger OnValidate()
            begin
                if "Plot-Based Order" then begin
                    if "Document Type" <> "Document Type"::"Blanket Order" then
                        Error('Plot-based orders are only supported for Blanket Orders.');
                    
                    if "Site Code" = '' then
                        Error('Site Code must be specified for plot-based orders.');
                end;
            end;
        }
        field(50103; "Total Plots"; Integer)
        {
            Caption = 'Total Plots';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Blanket Order Plot Lines" where("Document Type" = field("Document Type"), "Document No." = field("No.")));
        }
        field(50104; "Active Plots"; Integer)
        {
            Caption = 'Active Plots';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Blanket Order Plot Lines" where("Document Type" = field("Document Type"), "Document No." = field("No."), "Plot Status" = const(Active)));
        }
        field(50105; "Primary Contact Code"; Code[20])
        {
            Caption = 'Primary Contact Code';
            TableRelation = "Plot Contacts"."Contact Code" where("Site Code" = field("Site Code"));
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                PlotContacts: Record "Plot Contacts";
            begin
                if "Primary Contact Code" <> '' then begin
                    PlotContacts.Get("Primary Contact Code", "Site Code", '', '');
                    "Primary Contact Name" := PlotContacts."Contact Name";
                    "Primary Contact Phone" := PlotContacts."Phone No.";
                    "Primary Contact Email" := PlotContacts."E-Mail";
                end else begin
                    "Primary Contact Name" := '';
                    "Primary Contact Phone" := '';
                    "Primary Contact Email" := '';
                end;
            end;
        }
        field(50106; "Primary Contact Name"; Text[100])
        {
            Caption = 'Primary Contact Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50107; "Primary Contact Phone"; Text[30])
        {
            Caption = 'Primary Contact Phone';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50108; "Primary Contact Email"; Text[80])
        {
            Caption = 'Primary Contact Email';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50109; "Delivery Coordination Required"; Boolean)
        {
            Caption = 'Delivery Coordination Required';
            DataClassification = CustomerContent;
        }
        field(50110; "Multi-Location Delivery"; Boolean)
        {
            Caption = 'Multi-Location Delivery';
            DataClassification = CustomerContent;
        }
        field(50111; "Expected Start Date"; Date)
        {
            Caption = 'Expected Start Date';
            DataClassification = CustomerContent;
        }
        field(50112; "Expected Completion Date"; Date)
        {
            Caption = 'Expected Completion Date';
            DataClassification = CustomerContent;

            trigger OnValidate()
            begin
                if ("Expected Start Date" <> 0D) and ("Expected Completion Date" <> 0D) then
                    if "Expected Completion Date" < "Expected Start Date" then
                        Error('Expected Completion Date cannot be earlier than Expected Start Date.');
            end;
        }
        field(50113; "Delivery Instructions"; Text[250])
        {
            Caption = 'Delivery Instructions';
            DataClassification = CustomerContent;
        }
        field(50114; "Special Requirements"; Text[250])
        {
            Caption = 'Special Requirements';
            DataClassification = CustomerContent;
        }
    }
}
