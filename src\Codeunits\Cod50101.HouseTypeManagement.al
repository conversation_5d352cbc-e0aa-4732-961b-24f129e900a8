codeunit 50101 "House Type Management"
{
    /// <summary>
    /// Validates house type data and ensures business rules are followed
    /// </summary>
    procedure ValidateHouseTypeData(var HouseTypeMaster: Record "House Type Master")
    begin
        // Validate basic data
        if HouseTypeMaster."House Type Code" = '' then
            Error('House Type Code cannot be blank.');
        
        if HouseTypeMaster."House Type Name" = '' then
            Error('House Type Name cannot be blank.');

        // Validate numeric fields
        if HouseTypeMaster."Floor Area (sq ft)" < 0 then
            Error('Floor Area cannot be negative.');
        
        if HouseTypeMaster."No. of Bedrooms" < 0 then
            Error('Number of Bedrooms cannot be negative.');
        
        if HouseTypeMaster."No. of Bathrooms" < 0 then
            Error('Number of Bathrooms cannot be negative.');
        
        if HouseTypeMaster."Estimated Build Time (Days)" < 0 then
            Error('Estimated Build Time cannot be negative.');
    end;

    /// <summary>
    /// Copies materials from one house type to another
    /// </summary>
    procedure CopyMaterials(FromHouseTypeCode: Code[20]; ToHouseTypeCode: Code[20])
    var
        SourceMaterials: Record "House Type Materials";
        TargetMaterials: Record "House Type Materials";
        NextLineNo: Integer;
    begin
        if FromHouseTypeCode = ToHouseTypeCode then
            Error('Cannot copy materials to the same house type.');

        // Get next line number for target house type
        TargetMaterials.SetRange("House Type Code", ToHouseTypeCode);
        if TargetMaterials.FindLast() then
            NextLineNo := TargetMaterials."Line No." + 10000
        else
            NextLineNo := 10000;

        // Copy materials
        SourceMaterials.SetRange("House Type Code", FromHouseTypeCode);
        if SourceMaterials.FindSet() then
            repeat
                TargetMaterials := SourceMaterials;
                TargetMaterials."House Type Code" := ToHouseTypeCode;
                TargetMaterials."Line No." := NextLineNo;
                TargetMaterials.Insert(true);
                NextLineNo += 10000;
            until SourceMaterials.Next() = 0;
    end;

    /// <summary>
    /// Validates house type materials
    /// </summary>
    procedure ValidateMaterialData(var HouseTypeMaterials: Record "House Type Materials")
    var
        Item: Record Item;
        Vendor: Record Vendor;
    begin
        // Validate item exists
        if HouseTypeMaterials."Item No." <> '' then begin
            if not Item.Get(HouseTypeMaterials."Item No.") then
                Error('Item %1 does not exist.', HouseTypeMaterials."Item No.");
            
            if Item.Blocked then
                Error('Item %1 is blocked.', HouseTypeMaterials."Item No.");
        end;

        // Validate supplier exists
        if HouseTypeMaterials."Supplier No." <> '' then begin
            if not Vendor.Get(HouseTypeMaterials."Supplier No.") then
                Error('Vendor %1 does not exist.', HouseTypeMaterials."Supplier No.");
            
            if Vendor.Blocked <> Vendor.Blocked::" " then
                Error('Vendor %1 is blocked.', HouseTypeMaterials."Supplier No.");
        end;

        // Validate numeric fields
        if HouseTypeMaterials.Quantity < 0 then
            Error('Quantity cannot be negative.');
        
        if HouseTypeMaterials."Unit Cost" < 0 then
            Error('Unit Cost cannot be negative.');
        
        if HouseTypeMaterials."Lead Time (Days)" < 0 then
            Error('Lead Time cannot be negative.');
        
        if (HouseTypeMaterials."Waste Factor %" < 0) or (HouseTypeMaterials."Waste Factor %" > 100) then
            Error('Waste Factor must be between 0 and 100.');
    end;

    /// <summary>
    /// Calculates total material cost for a house type
    /// </summary>
    procedure CalculateTotalMaterialCost(HouseTypeCode: Code[20]): Decimal
    var
        HouseTypeMaterials: Record "House Type Materials";
        TotalCost: Decimal;
    begin
        TotalCost := 0;
        HouseTypeMaterials.SetRange("House Type Code", HouseTypeCode);
        if HouseTypeMaterials.FindSet() then
            repeat
                TotalCost += HouseTypeMaterials."Total Cost";
            until HouseTypeMaterials.Next() = 0;
        
        exit(TotalCost);
    end;

    /// <summary>
    /// Gets material statistics for a house type
    /// </summary>
    procedure GetMaterialStatistics(HouseTypeCode: Code[20]; var TotalLines: Integer; var CriticalItems: Integer; var TotalCost: Decimal)
    var
        HouseTypeMaterials: Record "House Type Materials";
    begin
        TotalLines := 0;
        CriticalItems := 0;
        TotalCost := 0;

        HouseTypeMaterials.SetRange("House Type Code", HouseTypeCode);
        if HouseTypeMaterials.FindSet() then
            repeat
                TotalLines += 1;
                if HouseTypeMaterials."Critical Item" then
                    CriticalItems += 1;
                TotalCost += HouseTypeMaterials."Total Cost";
            until HouseTypeMaterials.Next() = 0;
    end;

    /// <summary>
    /// Updates material costs from item master data
    /// </summary>
    procedure UpdateMaterialCosts(HouseTypeCode: Code[20])
    var
        HouseTypeMaterials: Record "House Type Materials";
        Item: Record Item;
        UpdatedCount: Integer;
    begin
        UpdatedCount := 0;
        HouseTypeMaterials.SetRange("House Type Code", HouseTypeCode);
        if HouseTypeMaterials.FindSet() then
            repeat
                if (HouseTypeMaterials."Item No." <> '') and Item.Get(HouseTypeMaterials."Item No.") then begin
                    if HouseTypeMaterials."Unit Cost" <> Item."Unit Cost" then begin
                        HouseTypeMaterials."Unit Cost" := Item."Unit Cost";
                        HouseTypeMaterials."Total Cost" := HouseTypeMaterials.Quantity * HouseTypeMaterials."Unit Cost";
                        HouseTypeMaterials.Modify();
                        UpdatedCount += 1;
                    end;
                end;
            until HouseTypeMaterials.Next() = 0;
        
        if UpdatedCount > 0 then
            Message('%1 material costs have been updated.', UpdatedCount)
        else
            Message('No material costs needed updating.');
    end;

    /// <summary>
    /// Validates that house type can be deleted
    /// </summary>
    procedure ValidateHouseTypeDeletion(HouseTypeCode: Code[20])
    var
        PlotMaster: Record "Plot Master";
    begin
        // Check if house type is assigned to any plots
        PlotMaster.SetRange("House Type Code", HouseTypeCode);
        if not PlotMaster.IsEmpty() then
            Error('Cannot delete house type %1 because it is assigned to plots.', HouseTypeCode);
    end;

    /// <summary>
    /// Creates a new house type based on an existing template
    /// </summary>
    procedure CreateFromTemplate(TemplateHouseTypeCode: Code[20]; NewHouseTypeCode: Code[20]; NewHouseTypeName: Text[100])
    var
        TemplateHouseType: Record "House Type Master";
        NewHouseType: Record "House Type Master";
    begin
        // Validate template exists
        if not TemplateHouseType.Get(TemplateHouseTypeCode) then
            Error('Template house type %1 does not exist.', TemplateHouseTypeCode);

        // Validate new house type doesn't exist
        if NewHouseType.Get(NewHouseTypeCode) then
            Error('House type %1 already exists.', NewHouseTypeCode);

        // Create new house type
        NewHouseType := TemplateHouseType;
        NewHouseType."House Type Code" := NewHouseTypeCode;
        NewHouseType."House Type Name" := NewHouseTypeName;
        NewHouseType.Status := NewHouseType.Status::Development;
        NewHouseType.Insert(true);

        // Copy materials
        CopyMaterials(TemplateHouseTypeCode, NewHouseTypeCode);
    end;
}
