page 50110 "Enhanced Blanket Sales Order"
{
    Caption = 'Enhanced Blanket Sales Order';
    PageType = Document;
    SourceTable = "Sales Header";
    SourceTableView = where("Document Type" = const("Blanket Order"));
    UsageCategory = Documents;
    ApplicationArea = All;

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the blanket order number.';
                }
                field("Sell-to Customer No."; Rec."Sell-to Customer No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer number.';
                }
                field("Sell-to Customer Name"; Rec."Sell-to Customer Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer name.';
                }
                field("Order Date"; Rec."Order Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the order date.';
                }
                field("Document Date"; Rec."Document Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the document date.';
                }
                field("Status"; Rec."Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the status of the blanket order.';
                }
            }
            group("Plot Information")
            {
                Caption = 'Plot Information';
                field("Plot-Based Order"; Rec."Plot-Based Order")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if this is a plot-based blanket order.';

                    trigger OnValidate()
                    var
                        BlanketOrderManagement: Codeunit "Blanket Order Management";
                    begin
                        BlanketOrderManagement.ValidatePlotBasedOrder(Rec);
                        CurrPage.Update();
                    end;
                }
                field("Site Code"; Rec."Site Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site code for this blanket order.';
                    Enabled = Rec."Plot-Based Order";
                }
                field("Site Name"; Rec."Site Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site name.';
                    Editable = false;
                }
                field("Total Plots"; Rec."Total Plots")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the total number of plots allocated to this order.';
                    Editable = false;
                }
                field("Active Plots"; Rec."Active Plots")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the number of active plots.';
                    Editable = false;
                }
            }
            group("Contact Information")
            {
                Caption = 'Contact Information';
                field("Primary Contact Code"; Rec."Primary Contact Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the primary contact for this order.';
                    Enabled = Rec."Plot-Based Order";
                }
                field("Primary Contact Name"; Rec."Primary Contact Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the primary contact name.';
                    Editable = false;
                }
                field("Primary Contact Phone"; Rec."Primary Contact Phone")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the primary contact phone number.';
                    Editable = false;
                }
                field("Primary Contact Email"; Rec."Primary Contact Email")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the primary contact email.';
                    Editable = false;
                }
            }
            group("Delivery Information")
            {
                Caption = 'Delivery Information';
                field("Multi-Location Delivery"; Rec."Multi-Location Delivery")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if this order requires multi-location delivery.';
                    Enabled = Rec."Plot-Based Order";
                }
                field("Delivery Coordination Required"; Rec."Delivery Coordination Required")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if delivery coordination is required.';
                    Enabled = Rec."Plot-Based Order";
                }
                field("Expected Start Date"; Rec."Expected Start Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected start date for deliveries.';
                    Enabled = Rec."Plot-Based Order";
                }
                field("Expected Completion Date"; Rec."Expected Completion Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected completion date for deliveries.';
                    Enabled = Rec."Plot-Based Order";
                }
                field("Delivery Instructions"; Rec."Delivery Instructions")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies special delivery instructions.';
                    MultiLine = true;
                    Enabled = Rec."Plot-Based Order";
                }
                field("Special Requirements"; Rec."Special Requirements")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies special requirements for this order.';
                    MultiLine = true;
                    Enabled = Rec."Plot-Based Order";
                }
            }
            part("Sales Lines"; "Blanket Sales Order Subform")
            {
                ApplicationArea = All;
                SubPageLink = "Document No." = field("No."), "Document Type" = field("Document Type");
                UpdatePropagation = Both;
            }
            part("Plot Lines"; "Blanket Order Plot Lines")
            {
                ApplicationArea = All;
                SubPageLink = "Document No." = field("No."), "Document Type" = field("Document Type");
                UpdatePropagation = Both;
                Visible = Rec."Plot-Based Order";
            }
        }
        area(factboxes)
        {
            part("Site Information"; "Site Information FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "Site Code" = field("Site Code");
                Visible = Rec."Plot-Based Order";
            }
            part("Customer Details"; "Plot Customer Details FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "No." = field("Sell-to Customer No.");
            }
        }
    }

    actions
    {
        area(processing)
        {
            group("Plot Management")
            {
                Caption = 'Plot Management';
                Visible = Rec."Plot-Based Order";
                action("Allocate Plots")
                {
                    ApplicationArea = All;
                    Caption = 'Allocate Plots';
                    Image = Allocate;
                    ToolTip = 'Allocate plots to this blanket order.';

                    trigger OnAction()
                    var
                        BlanketOrderManagement: Codeunit "Blanket Order Management";
                    begin
                        BlanketOrderManagement.AllocateLinesToPlots(Rec."Document Type", Rec."No.");
                        CurrPage.Update();
                    end;
                }
                action("Plot Grid View")
                {
                    ApplicationArea = All;
                    Caption = 'Plot Grid View';
                    Image = Grid;
                    ToolTip = 'View plots in a grid layout.';

                    trigger OnAction()
                    var
                        PlotGridView: Page "Plot Grid View";
                    begin
                        PlotGridView.SetSiteFilter(Rec."Site Code");
                        PlotGridView.Run();
                    end;
                }
                action("Delivery Locations")
                {
                    ApplicationArea = All;
                    Caption = 'Delivery Locations';
                    Image = Warehouse;
                    ToolTip = 'Manage delivery locations for plots.';

                    trigger OnAction()
                    var
                        DeliveryLocations: Record "Delivery Locations";
                        DeliveryLocationsList: Page "Delivery Locations List";
                    begin
                        DeliveryLocations.SetRange("Site Code", Rec."Site Code");
                        DeliveryLocationsList.SetTableView(DeliveryLocations);
                        DeliveryLocationsList.Run();
                    end;
                }
            }
            group("Communication")
            {
                Caption = 'Communication';
                Visible = Rec."Plot-Based Order";
                action("Contact Management")
                {
                    ApplicationArea = All;
                    Caption = 'Contact Management';
                    Image = ContactPerson;
                    ToolTip = 'Manage contacts for this order.';

                    trigger OnAction()
                    var
                        PlotContacts: Record "Plot Contacts";
                        PlotContactsList: Page "Plot Contacts List";
                    begin
                        PlotContacts.SetRange("Site Code", Rec."Site Code");
                        PlotContactsList.SetTableView(PlotContacts);
                        PlotContactsList.Run();
                    end;
                }
                action("Communication Log")
                {
                    ApplicationArea = All;
                    Caption = 'Communication Log';
                    Image = Log;
                    ToolTip = 'View communication log for this order.';

                    trigger OnAction()
                    var
                        CommunicationLog: Record "Communication Log";
                        CommunicationLogList: Page "Communication Log List";
                    begin
                        CommunicationLog.SetRange("Site Code", Rec."Site Code");
                        CommunicationLogList.SetTableView(CommunicationLog);
                        CommunicationLogList.Run();
                    end;
                }
            }
        }
        area(navigation)
        {
            group("Related Information")
            {
                Caption = 'Related Information';
                action("Site Card")
                {
                    ApplicationArea = All;
                    Caption = 'Site Card';
                    Image = Home;
                    ToolTip = 'View the site card.';
                    Enabled = Rec."Site Code" <> '';

                    trigger OnAction()
                    var
                        SiteCard: Page "Site Card";
                    begin
                        SiteCard.SetSiteCode(Rec."Site Code");
                        SiteCard.Run();
                    end;
                }
            }
        }
    }
}
