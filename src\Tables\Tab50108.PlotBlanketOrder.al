table 50109 "Plot Blanket Order"
{
    Caption = 'Plot Blanket Order';
    DataClassification = CustomerContent;
    LookupPageId = "Plot Blanket Order List";
    DrillDownPageId = "Plot Blanket Order List";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                NoSeriesMgt: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    GetSetup();
                    NoSeriesMgt.TestManual(Setup."Plot Blanket Order Nos.");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Sell-to Customer No."; Code[20])
        {
            Caption = 'Sell-to Customer No.';
            TableRelation = Customer."No.";
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                Customer: Record Customer;
            begin
                if "Sell-to Customer No." <> '' then begin
                    Customer.Get("Sell-to Customer No.");
                    "Sell-to Customer Name" := Customer.Name;
                    "Sell-to Address" := Customer.Address;
                    "Sell-to City" := Customer.City;
                    "Sell-to Post Code" := Customer."Post Code";
                    "Sell-to Country/Region Code" := Customer."Country/Region Code";
                    "Sell-to Contact" := Customer.Contact;
                end else begin
                    "Sell-to Customer Name" := '';
                    "Sell-to Address" := '';
                    "Sell-to City" := '';
                    "Sell-to Post Code" := '';
                    "Sell-to Country/Region Code" := '';
                    "Sell-to Contact" := '';
                end;
            end;
        }
        field(3; "Sell-to Customer Name"; Text[100])
        {
            Caption = 'Sell-to Customer Name';
            DataClassification = CustomerContent;
        }
        field(4; "Site Code"; Code[20])
        {
            Caption = 'Site Code';
            TableRelation = "Site Master"."Site Code" where("Customer No." = field("Sell-to Customer No."));
            DataClassification = CustomerContent;
            NotBlank = true;

            trigger OnValidate()
            var
                SiteMaster: Record "Site Master";
            begin
                if "Site Code" <> '' then begin
                    SiteMaster.Get("Site Code");
                    "Site Name" := SiteMaster."Site Name";

                    // Validate customer matches
                    if ("Sell-to Customer No." <> '') and ("Sell-to Customer No." <> SiteMaster."Customer No.") then
                        Error('Site %1 belongs to customer %2, but order is for customer %3.',
                            "Site Code", SiteMaster."Customer No.", "Sell-to Customer No.");
                end else
                    "Site Name" := '';
            end;
        }
        field(5; "Site Name"; Text[100])
        {
            Caption = 'Site Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(6; "Order Date"; Date)
        {
            Caption = 'Order Date';
            DataClassification = CustomerContent;
        }
        field(7; "Document Date"; Date)
        {
            Caption = 'Document Date';
            DataClassification = CustomerContent;
        }
        field(8; "Status"; Enum "Plot Order Status")
        {
            Caption = 'Status';
            DataClassification = CustomerContent;
        }
        field(9; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency.Code;
            DataClassification = CustomerContent;
        }
        field(10; "Expected Start Date"; Date)
        {
            Caption = 'Expected Start Date';
            DataClassification = CustomerContent;
        }
        field(11; "Expected Completion Date"; Date)
        {
            Caption = 'Expected Completion Date';
            DataClassification = CustomerContent;

            trigger OnValidate()
            begin
                if ("Expected Start Date" <> 0D) and ("Expected Completion Date" <> 0D) then
                    if "Expected Completion Date" < "Expected Start Date" then
                        Error('Expected Completion Date cannot be earlier than Expected Start Date.');
            end;
        }
        field(12; "Primary Contact Code"; Code[20])
        {
            Caption = 'Primary Contact Code';
            TableRelation = "Plot Contacts"."Contact Code" where("Site Code" = field("Site Code"));
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                PlotContacts: Record "Plot Contacts";
            begin
                if "Primary Contact Code" <> '' then begin
                    PlotContacts.SetRange("Contact Code", "Primary Contact Code");
                    PlotContacts.SetRange("Site Code", "Site Code");
                    if PlotContacts.FindFirst() then begin
                        "Primary Contact Name" := PlotContacts."Contact Name";
                        "Primary Contact Phone" := PlotContacts."Phone No.";
                        "Primary Contact Email" := PlotContacts."E-Mail";
                    end;
                end else begin
                    "Primary Contact Name" := '';
                    "Primary Contact Phone" := '';
                    "Primary Contact Email" := '';
                end;
            end;
        }
        field(13; "Primary Contact Name"; Text[100])
        {
            Caption = 'Primary Contact Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(14; "Primary Contact Phone"; Text[30])
        {
            Caption = 'Primary Contact Phone';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(15; "Primary Contact Email"; Text[80])
        {
            Caption = 'Primary Contact Email';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(16; "Delivery Instructions"; Text[250])
        {
            Caption = 'Delivery Instructions';
            DataClassification = CustomerContent;
        }
        field(17; "Special Requirements"; Text[250])
        {
            Caption = 'Special Requirements';
            DataClassification = CustomerContent;
        }
        field(18; "Multi-Location Delivery"; Boolean)
        {
            Caption = 'Multi-Location Delivery';
            DataClassification = CustomerContent;
        }
        field(19; "Delivery Coordination Required"; Boolean)
        {
            Caption = 'Delivery Coordination Required';
            DataClassification = CustomerContent;
        }
        field(20; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series".Code;
            DataClassification = CustomerContent;
        }
        field(21; "Sell-to Address"; Text[100])
        {
            Caption = 'Sell-to Address';
            DataClassification = CustomerContent;
        }
        field(22; "Sell-to City"; Text[30])
        {
            Caption = 'Sell-to City';
            DataClassification = CustomerContent;
        }
        field(23; "Sell-to Post Code"; Code[20])
        {
            Caption = 'Sell-to Post Code';
            DataClassification = CustomerContent;
        }
        field(24; "Sell-to Country/Region Code"; Code[10])
        {
            Caption = 'Sell-to Country/Region Code';
            TableRelation = "Country/Region".Code;
            DataClassification = CustomerContent;
        }
        field(25; "Sell-to Contact"; Text[100])
        {
            Caption = 'Sell-to Contact';
            DataClassification = CustomerContent;
        }
        field(26; "Total Plots"; Integer)
        {
            Caption = 'Total Plots';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Plot Blanket Order Lines" where("Document No." = field("No.")));
        }
        field(27; "Active Plots"; Integer)
        {
            Caption = 'Active Plots';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Plot Blanket Order Lines" where("Document No." = field("No."), "Plot Status" = const(Active)));
        }
        field(28; "Created Date"; Date)
        {
            Caption = 'Created Date';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(29; "Created By"; Code[50])
        {
            Caption = 'Created By';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(30; "Last Modified Date"; Date)
        {
            Caption = 'Last Modified Date';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(31; "Last Modified By"; Code[50])
        {
            Caption = 'Last Modified By';
            Editable = false;
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
        key(Customer; "Sell-to Customer No.", "Site Code")
        {
        }
        key(Site; "Site Code", "Status")
        {
        }
        key(Status; "Status", "Order Date")
        {
        }
    }

    trigger OnInsert()
    var
        NoSeriesMgt: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            GetSetup();
            NoSeriesMgt.InitSeries(Setup."Plot Blanket Order Nos.", xRec."No. Series", "Order Date", "No.", "No. Series");
        end;

        "Order Date" := WorkDate();
        "Document Date" := WorkDate();
        "Created Date" := Today;
        "Created By" := UserId;
        "Last Modified Date" := Today;
        "Last Modified By" := UserId;
        Status := Status::Open;
    end;

    trigger OnModify()
    begin
        "Last Modified Date" := Today;
        "Last Modified By" := UserId;
    end;

    var
        Setup: Record "Plot Management Setup";

    local procedure GetSetup()
    begin
        if not Setup.Get() then
            Setup.Insert(true);
    end;
}
