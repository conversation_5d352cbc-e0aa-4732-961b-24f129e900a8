codeunit 50102 "Plot Management Install"
{
    Subtype = Install;

    trigger OnInstallAppPerCompany()
    begin
        CreateSampleData();
    end;

    local procedure CreateSampleData()
    begin
        CreateSampleSite();
        CreateSampleHouseTypes();
        CreateSampleContacts();
        CreateSampleDeliveryLocations();
        Message('Plot Management extension installed successfully with Phase 2 sample data.');
    end;

    local procedure CreateSampleSite()
    var
        SiteMaster: Record "Site Master";
        PhaseMaster: Record "Phase Master";
        PlotMaster: Record "Plot Master";
        Customer: Record Customer;
    begin
        // Create a sample customer if it doesn't exist
        if not Customer.Get('SAMPLE001') then begin
            Customer.Init();
            Customer."No." := 'SAMPLE001';
            Customer.Name := 'Sample Housebuilder Ltd';
            Customer."Address" := '123 Builder Street';
            Customer."City" := 'London';
            Customer."Post Code" := 'SW1A 1AA';
            Customer."Country/Region Code" := 'GB';
            Customer.Insert(true);
        end;

        // Create sample site
        if not SiteMaster.Get('MEADOW001') then begin
            SiteMaster.Init();
            SiteMaster."Site Code" := 'MEADOW001';
            SiteMaster."Site Name" := 'Meadowbrook Development';
            SiteMaster."Customer No." := 'SAMPLE001';
            SiteMaster."Site Address" := 'Meadowbrook Lane';
            SiteMaster."City" := 'Cambridge';
            SiteMaster."Post Code" := 'CB1 2AB';
            SiteMaster."Country/Region Code" := 'GB';
            SiteMaster."Project Start Date" := Today;
            SiteMaster."Expected Completion Date" := CalcDate('<+2Y>', Today);
            SiteMaster.Status := SiteMaster.Status::Active;
            SiteMaster."Site Manager Name" := 'John Smith';
            SiteMaster."Site Manager Phone" := '01223 123456';
            SiteMaster."Site Manager Email" := '<EMAIL>';
            SiteMaster.Insert(true);
        end;

        // Create sample phase
        if not PhaseMaster.Get('PHASE1', 'MEADOW001') then begin
            PhaseMaster.Init();
            PhaseMaster."Phase Code" := 'PHASE1';
            PhaseMaster."Site Code" := 'MEADOW001';
            PhaseMaster."Phase Name" := 'Phase 1 - Foundation Plots';
            PhaseMaster."Phase Description" := 'Initial phase with 20 plots including infrastructure';
            PhaseMaster."Expected Start Date" := Today;
            PhaseMaster."Expected Completion Date" := CalcDate('<+1Y>', Today);
            PhaseMaster.Status := PhaseMaster.Status::Active;
            PhaseMaster."Sequence No." := 1;
            PhaseMaster."Phase Manager Name" := 'Sarah Johnson';
            PhaseMaster."Phase Manager Phone" := '01223 123457';
            PhaseMaster."Phase Manager Email" := '<EMAIL>';
            PhaseMaster.Insert(true);
        end;

        // Create sample plots
        CreateSamplePlots('MEADOW001', 'PHASE1');
    end;

    local procedure CreateSamplePlots(SiteCode: Code[20]; PhaseCode: Code[20])
    var
        PlotMaster: Record "Plot Master";
        i: Integer;
        PlotCode: Code[20];
    begin
        for i := 1 to 5 do begin
            PlotCode := SiteCode + '-' + PhaseCode + '-' + Format(i, 0, '<Integer,3><Filler Character,0>');
            if not PlotMaster.Get(PlotCode) then begin
                PlotMaster.Init();
                PlotMaster."Plot Code" := PlotCode;
                PlotMaster."Plot Number" := Format(i);
                PlotMaster."Site Code" := SiteCode;
                PlotMaster."Phase Code" := PhaseCode;
                PlotMaster."Plot Address" := Format(i) + ' Meadowbrook Lane';
                PlotMaster."City" := 'Cambridge';
                PlotMaster."Post Code" := 'CB1 2A' + Format(i);
                PlotMaster."Country/Region Code" := 'GB';
                PlotMaster."Expected Start Date" := CalcDate('<+' + Format(i * 30) + 'D>', Today);
                PlotMaster."Expected Completion Date" := CalcDate('<+' + Format((i * 30) + 120) + 'D>', Today);
                PlotMaster.Status := PlotMaster.Status::Planning;
                PlotMaster."Contact Name" := 'Site Foreman';
                PlotMaster."Contact Phone" := '01223 12345' + Format(i);
                PlotMaster."Sequence No." := i;
                PlotMaster."House Type Code" := 'DETACHED3';
                PlotMaster.Insert(true);
            end;
        end;
    end;

    local procedure CreateSampleHouseTypes()
    var
        HouseTypeMaster: Record "House Type Master";
        HouseTypeMaterials: Record "House Type Materials";
        Item: Record Item;
    begin
        // Create sample house type
        if not HouseTypeMaster.Get('DETACHED3') then begin
            HouseTypeMaster.Init();
            HouseTypeMaster."House Type Code" := 'DETACHED3';
            HouseTypeMaster."House Type Name" := '3-Bedroom Detached House';
            HouseTypeMaster."Description" := 'Standard 3-bedroom detached house with garage and garden';
            HouseTypeMaster."Category" := 'Detached';
            HouseTypeMaster."Floor Area (sq ft)" := 1200;
            HouseTypeMaster."No. of Bedrooms" := 3;
            HouseTypeMaster."No. of Bathrooms" := 2;
            HouseTypeMaster."Garage" := true;
            HouseTypeMaster."Garden" := true;
            HouseTypeMaster.Status := HouseTypeMaster.Status::Active;
            HouseTypeMaster."Estimated Build Time (Days)" := 120;
            HouseTypeMaster."Architect" := 'ABC Architects Ltd';
            HouseTypeMaster."Planning Reference" := 'PL/2024/001';
            HouseTypeMaster.Insert(true);
        end;

        // Create sample materials if items exist
        CreateSampleMaterials('DETACHED3');
    end;

    local procedure CreateSampleMaterials(HouseTypeCode: Code[20])
    var
        HouseTypeMaterials: Record "House Type Materials";
        Item: Record Item;
        LineNo: Integer;
    begin
        LineNo := 10000;

        // Try to find some common items and add them as materials
        Item.SetFilter("No.", '1000|1001|1002|1003|1004');
        if Item.FindSet() then
            repeat
                if not HouseTypeMaterials.Get(HouseTypeCode, LineNo) then begin
                    HouseTypeMaterials.Init();
                    HouseTypeMaterials."House Type Code" := HouseTypeCode;
                    HouseTypeMaterials."Line No." := LineNo;
                    HouseTypeMaterials."Item No." := Item."No.";
                    HouseTypeMaterials.Description := Item.Description;
                    HouseTypeMaterials."Description 2" := Item."Description 2";
                    HouseTypeMaterials."Unit of Measure Code" := Item."Base Unit of Measure";
                    HouseTypeMaterials."Unit Cost" := Item."Unit Cost";
                    HouseTypeMaterials.Quantity := 100; // Sample quantity
                    HouseTypeMaterials."Total Cost" := HouseTypeMaterials.Quantity * HouseTypeMaterials."Unit Cost";
                    HouseTypeMaterials."Material Category" := 'Building Materials';
                    HouseTypeMaterials."Installation Phase" := 'Foundation';
                    HouseTypeMaterials."Sequence No." := LineNo / 10000;
                    HouseTypeMaterials.Insert(true);
                    LineNo += 10000;
                end;
            until (Item.Next() = 0) or (LineNo > 50000);
    end;

    local procedure CreateSampleContacts()
    var
        PlotContacts: Record "Plot Contacts";
    begin
        // Create site-level contact
        if not PlotContacts.Get('SITEMGR001', 'MEADOW001', '', '') then begin
            PlotContacts.Init();
            PlotContacts."Contact Code" := 'SITEMGR001';
            PlotContacts."Site Code" := 'MEADOW001';
            PlotContacts."Phase Code" := '';
            PlotContacts."Plot Code" := '';
            PlotContacts."Contact Type" := PlotContacts."Contact Type"::"Site Manager";
            PlotContacts."Contact Name" := 'John Smith';
            PlotContacts."Job Title" := 'Site Manager';
            PlotContacts."Company Name" := 'Sample Housebuilder Ltd';
            PlotContacts."Phone No." := '01223 123456';
            PlotContacts."E-Mail" := '<EMAIL>';
            PlotContacts."Primary Contact" := true;
            PlotContacts."Contact Hierarchy Level" := PlotContacts."Contact Hierarchy Level"::"Site Level";
            PlotContacts."Preferred Contact Method" := PlotContacts."Preferred Contact Method"::Phone;
            PlotContacts.Insert(true);
        end;

        // Create phase-level contact
        if not PlotContacts.Get('PHASEMGR001', 'MEADOW001', 'PHASE1', '') then begin
            PlotContacts.Init();
            PlotContacts."Contact Code" := 'PHASEMGR001';
            PlotContacts."Site Code" := 'MEADOW001';
            PlotContacts."Phase Code" := 'PHASE1';
            PlotContacts."Plot Code" := '';
            PlotContacts."Contact Type" := PlotContacts."Contact Type"::"Phase Manager";
            PlotContacts."Contact Name" := 'Sarah Johnson';
            PlotContacts."Job Title" := 'Phase Manager';
            PlotContacts."Company Name" := 'Sample Housebuilder Ltd';
            PlotContacts."Phone No." := '01223 123457';
            PlotContacts."E-Mail" := '<EMAIL>';
            PlotContacts."Primary Contact" := true;
            PlotContacts."Contact Hierarchy Level" := PlotContacts."Contact Hierarchy Level"::"Phase Level";
            PlotContacts."Preferred Contact Method" := PlotContacts."Preferred Contact Method"::Email;
            PlotContacts.Insert(true);
        end;
    end;

    local procedure CreateSampleDeliveryLocations()
    var
        BlanketOrderManagement: Codeunit "Blanket Order Management";
        PlotMaster: Record "Plot Master";
    begin
        // Create delivery locations for sample plots
        PlotMaster.SetRange("Site Code", 'MEADOW001');
        PlotMaster.SetRange("Phase Code", 'PHASE1');
        if PlotMaster.FindSet() then
            repeat
                BlanketOrderManagement.CreateDefaultDeliveryLocations(PlotMaster."Plot Code");
            until PlotMaster.Next() = 0;
    end;
}
