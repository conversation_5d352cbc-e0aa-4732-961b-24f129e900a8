codeunit 50100 "Plot Management"
{
    /// <summary>
    /// Validates plot data and ensures business rules are followed
    /// </summary>
    procedure ValidatePlotData(var PlotMaster: Record "Plot Master")
    var
        SiteMaster: Record "Site Master";
        PhaseMaster: Record "Phase Master";
    begin
        // Validate site exists and is active
        if PlotMaster."Site Code" <> '' then begin
            if not SiteMaster.Get(PlotMaster."Site Code") then
                Error('Site %1 does not exist.', PlotMaster."Site Code");
            
            if SiteMaster.Status = SiteMaster.Status::Cancelled then
                Error('Cannot assign plot to cancelled site %1.', PlotMaster."Site Code");
        end;

        // Validate phase exists and belongs to the same site
        if (PlotMaster."Phase Code" <> '') and (PlotMaster."Site Code" <> '') then begin
            if not PhaseMaster.Get(PlotMaster."Phase Code", PlotMaster."Site Code") then
                Error('Phase %1 does not exist for site %2.', PlotMaster."Phase Code", PlotMaster."Site Code");
            
            if PhaseMaster.Status = PhaseMaster.Status::Cancelled then
                Error('Cannot assign plot to cancelled phase %1.', PlotMaster."Phase Code");
        end;

        // Validate dates
        ValidatePlotDates(PlotMaster);
    end;

    /// <summary>
    /// Validates plot dates are logical
    /// </summary>
    local procedure ValidatePlotDates(var PlotMaster: Record "Plot Master")
    begin
        // Expected dates validation
        if (PlotMaster."Expected Start Date" <> 0D) and (PlotMaster."Expected Completion Date" <> 0D) then
            if PlotMaster."Expected Completion Date" < PlotMaster."Expected Start Date" then
                Error('Expected Completion Date cannot be earlier than Expected Start Date.');

        // Actual dates validation
        if (PlotMaster."Actual Start Date" <> 0D) and (PlotMaster."Actual Completion Date" <> 0D) then
            if PlotMaster."Actual Completion Date" < PlotMaster."Actual Start Date" then
                Error('Actual Completion Date cannot be earlier than Actual Start Date.');

        // Cross validation - actual vs expected
        if (PlotMaster."Expected Start Date" <> 0D) and (PlotMaster."Actual Start Date" <> 0D) then
            if PlotMaster."Actual Start Date" < PlotMaster."Expected Start Date" then
                Message('Plot %1 started earlier than expected (%2 vs %3).', 
                    PlotMaster."Plot Code", 
                    PlotMaster."Actual Start Date", 
                    PlotMaster."Expected Start Date");
    end;

    /// <summary>
    /// Updates plot status based on dates and business rules
    /// </summary>
    procedure UpdatePlotStatus(var PlotMaster: Record "Plot Master")
    begin
        // Auto-update status based on dates
        if PlotMaster."Actual Completion Date" <> 0D then begin
            if PlotMaster.Status <> PlotMaster.Status::Completed then begin
                PlotMaster.Status := PlotMaster.Status::Completed;
                PlotMaster.Modify();
            end;
        end else if PlotMaster."Actual Start Date" <> 0D then begin
            if PlotMaster.Status = PlotMaster.Status::Planning then begin
                PlotMaster.Status := PlotMaster.Status::Active;
                PlotMaster.Modify();
            end;
        end;
    end;

    /// <summary>
    /// Generates a unique plot code based on site and phase
    /// </summary>
    procedure GeneratePlotCode(SiteCode: Code[20]; PhaseCode: Code[20]): Code[20]
    var
        PlotMaster: Record "Plot Master";
        NextNumber: Integer;
        PlotCode: Code[20];
    begin
        // Find the next available plot number for this site/phase combination
        PlotMaster.SetRange("Site Code", SiteCode);
        if PhaseCode <> '' then
            PlotMaster.SetRange("Phase Code", PhaseCode);
        
        if PlotMaster.FindLast() then begin
            // Extract number from last plot code and increment
            NextNumber := 1;
            repeat
                PlotCode := SiteCode + '-' + PhaseCode + '-' + Format(NextNumber, 0, '<Integer,3><Filler Character,0>');
                NextNumber += 1;
            until not PlotMaster.Get(PlotCode);
        end else
            PlotCode := SiteCode + '-' + PhaseCode + '-001';

        exit(PlotCode);
    end;

    /// <summary>
    /// Validates house type assignment to plot
    /// </summary>
    procedure ValidateHouseTypeAssignment(var PlotMaster: Record "Plot Master")
    var
        HouseTypeMaster: Record "House Type Master";
    begin
        if PlotMaster."House Type Code" <> '' then begin
            if not HouseTypeMaster.Get(PlotMaster."House Type Code") then
                Error('House Type %1 does not exist.', PlotMaster."House Type Code");
            
            if HouseTypeMaster.Status = HouseTypeMaster.Status::Obsolete then
                Error('Cannot assign obsolete house type %1 to plot.', PlotMaster."House Type Code");
            
            if HouseTypeMaster.Status = HouseTypeMaster.Status::Inactive then
                if not Confirm('House Type %1 is inactive. Do you want to continue?', false, PlotMaster."House Type Code") then
                    Error('');
        end;
    end;

    /// <summary>
    /// Gets plot statistics for a site
    /// </summary>
    procedure GetSiteStatistics(SiteCode: Code[20]; var TotalPlots: Integer; var ActivePlots: Integer; var CompletedPlots: Integer)
    var
        PlotMaster: Record "Plot Master";
    begin
        TotalPlots := 0;
        ActivePlots := 0;
        CompletedPlots := 0;

        PlotMaster.SetRange("Site Code", SiteCode);
        if PlotMaster.FindSet() then
            repeat
                TotalPlots += 1;
                case PlotMaster.Status of
                    PlotMaster.Status::Active:
                        ActivePlots += 1;
                    PlotMaster.Status::Completed:
                        CompletedPlots += 1;
                end;
            until PlotMaster.Next() = 0;
    end;

    /// <summary>
    /// Gets phase statistics for a site and phase
    /// </summary>
    procedure GetPhaseStatistics(SiteCode: Code[20]; PhaseCode: Code[20]; var TotalPlots: Integer; var ActivePlots: Integer; var CompletedPlots: Integer)
    var
        PlotMaster: Record "Plot Master";
    begin
        TotalPlots := 0;
        ActivePlots := 0;
        CompletedPlots := 0;

        PlotMaster.SetRange("Site Code", SiteCode);
        PlotMaster.SetRange("Phase Code", PhaseCode);
        if PlotMaster.FindSet() then
            repeat
                TotalPlots += 1;
                case PlotMaster.Status of
                    PlotMaster.Status::Active:
                        ActivePlots += 1;
                    PlotMaster.Status::Completed:
                        CompletedPlots += 1;
                end;
            until PlotMaster.Next() = 0;
    end;
}
