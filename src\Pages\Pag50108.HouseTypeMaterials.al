page 50108 "House Type Materials"
{
    ApplicationArea = All;
    Caption = 'House Type Materials';
    PageType = List;
    SourceTable = "House Type Materials";
    AutoSplitKey = true;

    layout
    {
        area(content)
        {
            repeater(General)
            {
                field("Line No."; Rec."Line No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the line number.';
                    Visible = false;
                }
                field("Item No."; Rec."Item No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the item number.';
                }
                field("Description"; Rec."Description")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the description of the item.';
                }
                field("Description 2"; Rec."Description 2")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies additional description of the item.';
                }
                field("Material Category"; Rec."Material Category")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the material category.';
                }
                field("Installation Phase"; Rec."Installation Phase")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the installation phase.';
                }
                field("Quantity"; Rec."Quantity")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the quantity required.';
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the unit of measure code.';
                }
                field("Unit Cost"; Rec."Unit Cost")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the unit cost.';
                }
                field("Total Cost"; Rec."Total Cost")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the total cost.';
                }
                field("Waste Factor %"; Rec."Waste Factor %")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the waste factor percentage.';
                }
                field("Critical Item"; Rec."Critical Item")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if this is a critical item.';
                }
                field("Lead Time (Days)"; Rec."Lead Time (Days)")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the lead time in days.';
                }
                field("Supplier No."; Rec."Supplier No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the supplier number.';
                }
                field("Supplier Name"; Rec."Supplier Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the supplier name.';
                }
                field("Sequence No."; Rec."Sequence No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the sequence number for ordering.';
                }
                field("Alternative Item 1"; Rec."Alternative Item 1")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the first alternative item.';
                }
                field("Alternative Item 2"; Rec."Alternative Item 2")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the second alternative item.';
                }
                field("Notes"; Rec."Notes")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies additional notes.';
                }
            }
        }
        area(factboxes)
        {
            part(ItemPicture; "Item Picture")
            {
                ApplicationArea = All;
                SubPageLink = "No." = field("Item No.");
            }
            systempart(Control1900383207; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(Control1905767507; Notes)
            {
                ApplicationArea = Notes;
            }
        }
    }

    actions
    {
        area(processing)
        {
            group("Functions")
            {
                Caption = 'Functions';
                action("Get Items from Template")
                {
                    ApplicationArea = All;
                    Caption = 'Get Items from Template';
                    Image = GetLines;
                    ToolTip = 'Get items from another house type template.';
                    
                    trigger OnAction()
                    var
                        HouseTypeMaster: Record "House Type Master";
                        SourceHouseTypeMaterials: Record "House Type Materials";
                        NewHouseTypeMaterials: Record "House Type Materials";
                        HouseTypeList: Page "House Type List";
                        NextLineNo: Integer;
                    begin
                        HouseTypeList.LookupMode(true);
                        if HouseTypeList.RunModal() = Action::LookupOK then begin
                            HouseTypeList.GetRecord(HouseTypeMaster);
                            
                            // Get next line number
                            NewHouseTypeMaterials.SetRange("House Type Code", Rec."House Type Code");
                            if NewHouseTypeMaterials.FindLast() then
                                NextLineNo := NewHouseTypeMaterials."Line No." + 10000
                            else
                                NextLineNo := 10000;
                            
                            // Copy materials from selected house type
                            SourceHouseTypeMaterials.SetRange("House Type Code", HouseTypeMaster."House Type Code");
                            if SourceHouseTypeMaterials.FindSet() then
                                repeat
                                    NewHouseTypeMaterials := SourceHouseTypeMaterials;
                                    NewHouseTypeMaterials."House Type Code" := Rec."House Type Code";
                                    NewHouseTypeMaterials."Line No." := NextLineNo;
                                    NewHouseTypeMaterials.Insert(true);
                                    NextLineNo += 10000;
                                until SourceHouseTypeMaterials.Next() = 0;
                            
                            CurrPage.Update(false);
                            Message('Materials copied from house type %1.', HouseTypeMaster."House Type Code");
                        end;
                    end;
                }
                action("Calculate Total Cost")
                {
                    ApplicationArea = All;
                    Caption = 'Calculate Total Cost';
                    Image = Calculate;
                    ToolTip = 'Recalculate total costs for all lines.';
                    
                    trigger OnAction()
                    var
                        HouseTypeMaterials: Record "House Type Materials";
                    begin
                        HouseTypeMaterials.SetRange("House Type Code", Rec."House Type Code");
                        if HouseTypeMaterials.FindSet() then
                            repeat
                                HouseTypeMaterials."Total Cost" := HouseTypeMaterials.Quantity * HouseTypeMaterials."Unit Cost";
                                HouseTypeMaterials.Modify();
                            until HouseTypeMaterials.Next() = 0;
                        
                        CurrPage.Update(false);
                        Message('Total costs have been recalculated.');
                    end;
                }
            }
        }
        area(reporting)
        {
            action("Material List")
            {
                ApplicationArea = All;
                Caption = 'Material List';
                Image = Report;
                ToolTip = 'Print the material list for this house type.';
                
                trigger OnAction()
                begin
                    Message('Material List report will be implemented in Phase 8.');
                end;
            }
        }
    }
}
