page 50111 "Blanket Order Plot Lines"
{
    Caption = 'Blanket Order Plot Lines';
    PageType = ListPart;
    SourceTable = "Blanket Order Plot Lines";
    AutoSplitKey = true;

    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                field("Plot Code"; Rec."Plot Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot code.';
                }
                field("Plot Name"; Rec."Plot Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot name.';
                }
                field("Phase Code"; Rec."Phase Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phase code.';
                }
                field("House Type Code"; Rec."House Type Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the house type code.';
                }
                field("Plot Status"; Rec."Plot Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot status.';
                }
                field("Allocation Status"; Rec."Allocation Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the allocation status.';
                }
                field("Total Allocated Lines"; Rec."Total Allocated Lines")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the total number of allocated lines.';
                }
                field("Total Allocated Amount"; Rec."Total Allocated Amount")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the total allocated amount.';
                }
                field("Primary Contact Code"; Rec."Primary Contact Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the primary contact code.';
                }
                field("Expected Start Date"; Rec."Expected Start Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected start date.';
                }
                field("Expected Completion Date"; Rec."Expected Completion Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected completion date.';
                }
                field("Delivery Instructions"; Rec."Delivery Instructions")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies delivery instructions.';
                }
            }
        }
    }

    actions
    {
        area(processing)
        {
            action("Plot Details")
            {
                ApplicationArea = All;
                Caption = 'Plot Details';
                Image = Card;
                ToolTip = 'View plot details.';
                
                trigger OnAction()
                var
                    PlotCard: Page "Plot Card";
                begin
                    PlotCard.SetPlotCode(Rec."Plot Code");
                    PlotCard.Run();
                end;
            }
            action("Allocated Lines")
            {
                ApplicationArea = All;
                Caption = 'Allocated Lines';
                Image = Line;
                ToolTip = 'View allocated sales lines for this plot.';
                
                trigger OnAction()
                var
                    SalesLine: Record "Sales Line";
                    SalesLines: Page "Sales Lines";
                begin
                    SalesLine.SetRange("Document Type", Rec."Document Type");
                    SalesLine.SetRange("Document No.", Rec."Document No.");
                    SalesLine.SetRange("Plot Code", Rec."Plot Code");
                    SalesLines.SetTableView(SalesLine);
                    SalesLines.Run();
                end;
            }
            action("Delivery Locations")
            {
                ApplicationArea = All;
                Caption = 'Delivery Locations';
                Image = Warehouse;
                ToolTip = 'Manage delivery locations for this plot.';
                
                trigger OnAction()
                var
                    DeliveryLocations: Record "Delivery Locations";
                    DeliveryLocationsList: Page "Delivery Locations List";
                begin
                    DeliveryLocations.SetRange("Plot Code", Rec."Plot Code");
                    DeliveryLocationsList.SetTableView(DeliveryLocations);
                    DeliveryLocationsList.Run();
                end;
            }
            action("Communication Log")
            {
                ApplicationArea = All;
                Caption = 'Communication Log';
                Image = Log;
                ToolTip = 'View communication log for this plot.';
                
                trigger OnAction()
                var
                    CommunicationLog: Record "Communication Log";
                    CommunicationLogList: Page "Communication Log List";
                begin
                    CommunicationLog.SetRange("Site Code", Rec."Site Code");
                    CommunicationLog.SetRange("Phase Code", Rec."Phase Code");
                    CommunicationLog.SetRange("Plot Code", Rec."Plot Code");
                    CommunicationLogList.SetTableView(CommunicationLog);
                    CommunicationLogList.Run();
                end;
            }
        }
    }
}
