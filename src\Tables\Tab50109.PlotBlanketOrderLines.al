table 50109 "Plot Blanket Order Lines"
{
    Caption = 'Plot Blanket Order Lines';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            TableRelation = "Plot Blanket Order"."No.";
            DataClassification = CustomerContent;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            DataClassification = CustomerContent;
        }
        field(3; "Plot Code"; Code[20])
        {
            Caption = 'Plot Code';
            TableRelation = "Plot Master"."Plot Code";
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                PlotMaster: Record "Plot Master";
                PlotBlanketOrder: Record "Plot Blanket Order";
            begin
                if "Plot Code" <> '' then begin
                    PlotMaster.Get("Plot Code");
                    "Plot Name" := PlotMaster."Plot Number";
                    "Site Code" := PlotMaster."Site Code";
                    "Phase Code" := PlotMaster."Phase Code";
                    "House Type Code" := PlotMaster."House Type Code";
                    "Plot Status" := PlotMaster.Status;
                    
                    // Validate against document header
                    if PlotBlanketOrder.Get("Document No.") then begin
                        if PlotBlanketOrder."Site Code" <> PlotMaster."Site Code" then
                            Error('Plot %1 belongs to site %2, but document is for site %3.', 
                                "Plot Code", PlotMaster."Site Code", PlotBlanketOrder."Site Code");
                    end;
                end else begin
                    "Plot Name" := '';
                    "Site Code" := '';
                    "Phase Code" := '';
                    "House Type Code" := '';
                    "Plot Status" := "Plot Status"::" ";
                end;
            end;
        }
        field(4; "Plot Name"; Text[50])
        {
            Caption = 'Plot Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(5; "Site Code"; Code[20])
        {
            Caption = 'Site Code';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(6; "Phase Code"; Code[20])
        {
            Caption = 'Phase Code';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(7; "House Type Code"; Code[20])
        {
            Caption = 'House Type Code';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(8; "Plot Status"; Enum "Plot Status")
        {
            Caption = 'Plot Status';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(9; "Allocation Status"; Enum "Plot Allocation Status")
        {
            Caption = 'Allocation Status';
            DataClassification = CustomerContent;
        }
        field(10; "Expected Delivery Date"; Date)
        {
            Caption = 'Expected Delivery Date';
            DataClassification = CustomerContent;
        }
        field(11; "Actual Delivery Date"; Date)
        {
            Caption = 'Actual Delivery Date';
            DataClassification = CustomerContent;
        }
        field(12; "Delivery Location Code"; Code[30])
        {
            Caption = 'Delivery Location Code';
            TableRelation = "Delivery Locations"."Location Code" where("Plot Code" = field("Plot Code"));
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                DeliveryLocations: Record "Delivery Locations";
            begin
                if "Delivery Location Code" <> '' then begin
                    DeliveryLocations.Get("Delivery Location Code", "Plot Code");
                    "Delivery Location Name" := DeliveryLocations."Location Name";
                end else
                    "Delivery Location Name" := '';
            end;
        }
        field(13; "Delivery Location Name"; Text[100])
        {
            Caption = 'Delivery Location Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(14; "Special Instructions"; Text[250])
        {
            Caption = 'Special Instructions';
            DataClassification = CustomerContent;
        }
        field(15; "Priority"; Integer)
        {
            Caption = 'Priority';
            DataClassification = CustomerContent;
        }
        field(16; "Sequence No."; Integer)
        {
            Caption = 'Sequence No.';
            DataClassification = CustomerContent;
        }
        field(17; "Total Sales Lines"; Integer)
        {
            Caption = 'Total Sales Lines';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Sales Line" where("Plot Code" = field("Plot Code"), "Document Type" = const("Blanket Order")));
        }
        field(18; "Total Amount"; Decimal)
        {
            Caption = 'Total Amount';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Sales Line"."Line Amount" where("Plot Code" = field("Plot Code"), "Document Type" = const("Blanket Order")));
        }
        field(19; "Created Date"; Date)
        {
            Caption = 'Created Date';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(20; "Created By"; Code[50])
        {
            Caption = 'Created By';
            Editable = false;
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(Plot; "Plot Code", "Allocation Status")
        {
        }
        key(Site; "Site Code", "Phase Code", "Plot Code")
        {
        }
        key(Priority; "Priority", "Sequence No.")
        {
        }
        key(Delivery; "Expected Delivery Date", "Plot Code")
        {
        }
    }

    trigger OnInsert()
    begin
        "Created Date" := Today;
        "Created By" := UserId;
        "Allocation Status" := "Allocation Status"::"Not Allocated";
    end;
}
