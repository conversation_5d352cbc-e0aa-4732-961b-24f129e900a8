page 50124 "Plot Blanket Order Card"
{
    ApplicationArea = All;
    Caption = 'Plot Blanket Order';
    PageType = Document;
    SourceTable = "Plot Blanket Order";

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot blanket order number.';

                    trigger OnAssistEdit()
                    var
                        NoSeriesMgt: Codeunit "No. Series";
                    begin
                        if NoSeriesMgt.SelectSeries(GetNoSeriesCode(), xRec."No. Series", Rec."No. Series") then begin
                            NoSeriesMgt.SetSeries(Rec."No.");
                            Rec.Modify();
                        end;
                    end;
                }
                field("Sell-to Customer No."; Rec."Sell-to Customer No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer number.';
                    ShowMandatory = true;
                }
                field("Sell-to Customer Name"; Rec."Sell-to Customer Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer name.';
                }
                field("Site Code"; Rec."Site Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site code.';
                    ShowMandatory = true;
                }
                field("Site Name"; Rec."Site Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site name.';
                }
                field("Order Date"; Rec."Order Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the order date.';
                }
                field("Document Date"; Rec."Document Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the document date.';
                }
                field("Status"; Rec."Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the order status.';
                }
                field("Currency Code"; Rec."Currency Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the currency code.';
                }
            }
            group("Delivery Information")
            {
                Caption = 'Delivery Information';
                field("Expected Start Date"; Rec."Expected Start Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected start date.';
                }
                field("Expected Completion Date"; Rec."Expected Completion Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected completion date.';
                }
                field("Multi-Location Delivery"; Rec."Multi-Location Delivery")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if multi-location delivery is required.';
                }
                field("Delivery Coordination Required"; Rec."Delivery Coordination Required")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if delivery coordination is required.';
                }
                field("Delivery Instructions"; Rec."Delivery Instructions")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies delivery instructions.';
                    MultiLine = true;
                }
                field("Special Requirements"; Rec."Special Requirements")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies special requirements.';
                    MultiLine = true;
                }
            }
            group("Contact Information")
            {
                Caption = 'Contact Information';
                field("Primary Contact Code"; Rec."Primary Contact Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the primary contact code.';
                }
                field("Primary Contact Name"; Rec."Primary Contact Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the primary contact name.';
                }
                field("Primary Contact Phone"; Rec."Primary Contact Phone")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the primary contact phone.';
                }
                field("Primary Contact Email"; Rec."Primary Contact Email")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the primary contact email.';
                }
            }
            group("Customer Address")
            {
                Caption = 'Customer Address';
                field("Sell-to Address"; Rec."Sell-to Address")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer address.';
                }
                field("Sell-to City"; Rec."Sell-to City")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer city.';
                }
                field("Sell-to Post Code"; Rec."Sell-to Post Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer post code.';
                }
                field("Sell-to Country/Region Code"; Rec."Sell-to Country/Region Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer country/region code.';
                }
                field("Sell-to Contact"; Rec."Sell-to Contact")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer contact.';
                }
            }
            part("Plot Lines"; "Plot Blanket Order Lines")
            {
                ApplicationArea = All;
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
            }
        }
        area(factboxes)
        {
            part("Site Information"; "Site Information FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "Site Code" = field("Site Code");
            }
            part("Customer Details"; "Plot Customer Details FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "No." = field("Sell-to Customer No.");
            }
        }
    }

    actions
    {
        area(processing)
        {
            group("Plot Management")
            {
                Caption = 'Plot Management';
                action("Allocate Plots")
                {
                    ApplicationArea = All;
                    Caption = 'Allocate Plots';
                    Image = Allocate;
                    ToolTip = 'Allocate plots to this order.';

                    trigger OnAction()
                    var
                        PlotBlanketOrderMgt: Codeunit "Plot Blanket Order Management";
                    begin
                        PlotBlanketOrderMgt.AllocateLinesToPlots(Rec."No.");
                        CurrPage.Update();
                    end;
                }
                action("Plot Grid View")
                {
                    ApplicationArea = All;
                    Caption = 'Plot Grid View';
                    Image = Grid;
                    ToolTip = 'View plots in a grid layout.';

                    trigger OnAction()
                    var
                        PlotGridView: Page "Plot Grid View";
                    begin
                        PlotGridView.SetSiteFilter(Rec."Site Code");
                        PlotGridView.Run();
                    end;
                }
                action("Create Sales Orders")
                {
                    ApplicationArea = All;
                    Caption = 'Create Sales Orders';
                    Image = MakeOrder;
                    ToolTip = 'Create sales orders from this plot blanket order.';

                    trigger OnAction()
                    var
                        PlotBlanketOrderMgt: Codeunit "Plot Blanket Order Management";
                    begin
                        PlotBlanketOrderMgt.CreateSalesOrders(Rec."No.");
                        Message('Sales orders created successfully.');
                    end;
                }
            }
            group("Release")
            {
                Caption = 'Release';
                action("Release Order")
                {
                    ApplicationArea = All;
                    Caption = 'Release';
                    Image = ReleaseDoc;
                    ToolTip = 'Release the plot blanket order.';

                    trigger OnAction()
                    begin
                        if Rec.Status = Rec.Status::Open then begin
                            Rec.Status := Rec.Status::Released;
                            Rec.Modify();
                            CurrPage.Update();
                            Message('Plot blanket order %1 has been released.', Rec."No.");
                        end;
                    end;
                }
                action("Reopen Order")
                {
                    ApplicationArea = All;
                    Caption = 'Reopen';
                    Image = ReOpen;
                    ToolTip = 'Reopen the plot blanket order.';

                    trigger OnAction()
                    begin
                        if Rec.Status = Rec.Status::Released then begin
                            Rec.Status := Rec.Status::Open;
                            Rec.Modify();
                            CurrPage.Update();
                            Message('Plot blanket order %1 has been reopened.', Rec."No.");
                        end;
                    end;
                }
            }
        }
        area(navigation)
        {
            group("Order")
            {
                Caption = 'Order';
                action("Site Card")
                {
                    ApplicationArea = All;
                    Caption = 'Site Card';
                    Image = Home;
                    RunObject = Page "Site Card";
                    RunPageLink = "Site Code" = field("Site Code");
                    ToolTip = 'View the site card.';
                }
                action("Customer Card")
                {
                    ApplicationArea = All;
                    Caption = 'Customer Card';
                    Image = Customer;
                    RunObject = Page "Customer Card";
                    RunPageLink = "No." = field("Sell-to Customer No.");
                    ToolTip = 'View the customer card.';
                }
                action("Communication Log")
                {
                    ApplicationArea = All;
                    Caption = 'Communication Log';
                    Image = CompanyInformation;
                    RunObject = Page "Communication Log List";
                    RunPageLink = "Document Type" = const("Plot Blanket Order"), "Document No." = field("No.");
                    ToolTip = 'View communication log for this order.';
                }
            }
        }
    }

    local procedure GetNoSeriesCode(): Code[20]
    var
        Setup: Record "Plot Management Setup";
    begin
        Setup.Get();
        exit(Setup."Plot Blanket Order Nos.");
    end;
}
