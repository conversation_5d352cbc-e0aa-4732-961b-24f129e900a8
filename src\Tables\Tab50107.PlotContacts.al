table 50107 "Plot Contacts"
{
    Caption = 'Plot Contacts';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Contact Code"; Code[20])
        {
            Caption = 'Contact Code';
            DataClassification = CustomerContent;
        }
        field(2; "Site Code"; Code[20])
        {
            Caption = 'Site Code';
            TableRelation = "Site Master"."Site Code";
            DataClassification = CustomerContent;
        }
        field(3; "Phase Code"; Code[20])
        {
            Caption = 'Phase Code';
            TableRelation = "Phase Master"."Phase Code" where("Site Code" = field("Site Code"));
            DataClassification = CustomerContent;
        }
        field(4; "Plot Code"; Code[20])
        {
            Caption = 'Plot Code';
            TableRelation = "Plot Master"."Plot Code" where("Site Code" = field("Site Code"), "Phase Code" = field("Phase Code"));
            DataClassification = CustomerContent;
        }
        field(5; "Contact Type"; Enum "Plot Contact Type")
        {
            Caption = 'Contact Type';
            DataClassification = CustomerContent;
        }
        field(6; "Contact Name"; Text[100])
        {
            Caption = 'Contact Name';
            DataClassification = CustomerContent;
        }
        field(7; "Job Title"; Text[50])
        {
            Caption = 'Job Title';
            DataClassification = CustomerContent;
        }
        field(8; "Company Name"; Text[100])
        {
            Caption = 'Company Name';
            DataClassification = CustomerContent;
        }
        field(9; "Phone No."; Text[30])
        {
            Caption = 'Phone No.';
            DataClassification = CustomerContent;
        }
        field(10; "Mobile No."; Text[30])
        {
            Caption = 'Mobile No.';
            DataClassification = CustomerContent;
        }
        field(11; "E-Mail"; Text[80])
        {
            Caption = 'E-Mail';
            DataClassification = CustomerContent;
        }
        field(12; "Address"; Text[100])
        {
            Caption = 'Address';
            DataClassification = CustomerContent;
        }
        field(13; "Address 2"; Text[50])
        {
            Caption = 'Address 2';
            DataClassification = CustomerContent;
        }
        field(14; "City"; Text[30])
        {
            Caption = 'City';
            DataClassification = CustomerContent;
        }
        field(15; "Post Code"; Code[20])
        {
            Caption = 'Post Code';
            DataClassification = CustomerContent;
        }
        field(16; "County"; Text[30])
        {
            Caption = 'County';
            DataClassification = CustomerContent;
        }
        field(17; "Country/Region Code"; Code[10])
        {
            Caption = 'Country/Region Code';
            TableRelation = "Country/Region";
            DataClassification = CustomerContent;
        }
        field(18; "Primary Contact"; Boolean)
        {
            Caption = 'Primary Contact';
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                PlotContacts: Record "Plot Contacts";
            begin
                if "Primary Contact" then begin
                    // Ensure only one primary contact per plot/phase/site level
                    PlotContacts.SetRange("Site Code", "Site Code");
                    PlotContacts.SetRange("Phase Code", "Phase Code");
                    PlotContacts.SetRange("Plot Code", "Plot Code");
                    PlotContacts.SetFilter("Contact Code", '<>%1', "Contact Code");
                    PlotContacts.SetRange("Primary Contact", true);
                    if PlotContacts.FindSet() then
                        repeat
                            PlotContacts."Primary Contact" := false;
                            PlotContacts.Modify();
                        until PlotContacts.Next() = 0;
                end;
            end;
        }
        field(19; "Active"; Boolean)
        {
            Caption = 'Active';
            DataClassification = CustomerContent;
        }
        field(20; "Contact Hierarchy Level"; Enum "Contact Hierarchy Level")
        {
            Caption = 'Contact Hierarchy Level';
            DataClassification = CustomerContent;
        }
        field(21; "BC Contact No."; Code[20])
        {
            Caption = 'BC Contact No.';
            TableRelation = Contact."No.";
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                Contact: Record Contact;
            begin
                if "BC Contact No." <> '' then begin
                    Contact.Get("BC Contact No.");
                    "Contact Name" := Contact.Name;
                    "Company Name" := Contact."Company Name";
                    "Phone No." := Contact."Phone No.";
                    "E-Mail" := Contact."E-Mail";
                    "Address" := Contact.Address;
                    "Address 2" := Contact."Address 2";
                    "City" := Contact.City;
                    "Post Code" := Contact."Post Code";
                    "County" := Contact.County;
                    "Country/Region Code" := Contact."Country/Region Code";
                end;
            end;
        }
        field(22; "Preferred Contact Method"; Enum "Preferred Contact Method")
        {
            Caption = 'Preferred Contact Method';
            DataClassification = CustomerContent;
        }
        field(23; "Available Hours"; Text[100])
        {
            Caption = 'Available Hours';
            DataClassification = CustomerContent;
        }
        field(24; "Notes"; Text[250])
        {
            Caption = 'Notes';
            DataClassification = CustomerContent;
        }
        field(25; "Created Date"; Date)
        {
            Caption = 'Created Date';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(26; "Created By"; Code[50])
        {
            Caption = 'Created By';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(27; "Last Modified Date"; Date)
        {
            Caption = 'Last Modified Date';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(28; "Last Modified By"; Code[50])
        {
            Caption = 'Last Modified By';
            Editable = false;
            DataClassification = CustomerContent;
        }
    }

    keys
    {
        key(PK; "Contact Code", "Site Code", "Phase Code", "Plot Code")
        {
            Clustered = true;
        }
        key(Site; "Site Code", "Contact Type", "Primary Contact")
        {
        }
        key(Phase; "Site Code", "Phase Code", "Contact Type", "Primary Contact")
        {
        }
        key(Plot; "Site Code", "Phase Code", "Plot Code", "Contact Type", "Primary Contact")
        {
        }
        key(BCContact; "BC Contact No.")
        {
        }
        key(Hierarchy; "Contact Hierarchy Level", "Contact Type")
        {
        }
    }

    trigger OnInsert()
    begin
        "Created Date" := Today;
        "Created By" := UserId;
        "Last Modified Date" := Today;
        "Last Modified By" := UserId;
        "Active" := true;
    end;

    trigger OnModify()
    begin
        "Last Modified Date" := Today;
        "Last Modified By" := UserId;
    end;
}
