tableextension 50101 "Sales Line Extension" extends "Sales Line"
{
    fields
    {
        field(50100; "Plot Code"; Code[20])
        {
            Caption = 'Plot Code';
            TableRelation = "Plot Master"."Plot Code" where("Site Code" = field("Site Code"));
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                PlotMaster: Record "Plot Master";
                SalesHeader: Record "Sales Header";
            begin
                if "Plot Code" <> '' then begin
                    PlotMaster.Get("Plot Code");
                    "Plot Name" := PlotMaster."Plot Number";
                    "Phase Code" := PlotMaster."Phase Code";
                    "Site Code" := PlotMaster."Site Code";
                    "House Type Code" := PlotMaster."House Type Code";
                    
                    // Validate against header
                    if SalesHeader.Get("Document Type", "Document No.") then begin
                        if SalesHeader."Site Code" <> PlotMaster."Site Code" then
                            Error('Plot %1 belongs to site %2, but order is for site %3.', 
                                "Plot Code", PlotMaster."Site Code", SalesHeader."Site Code");
                    end;
                end else begin
                    "Plot Name" := '';
                    "Phase Code" := '';
                    "House Type Code" := '';
                end;
            end;
        }
        field(50101; "Plot Name"; Text[50])
        {
            Caption = 'Plot Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50102; "Phase Code"; Code[20])
        {
            Caption = 'Phase Code';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50103; "Site Code"; Code[20])
        {
            Caption = 'Site Code';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50104; "House Type Code"; Code[20])
        {
            Caption = 'House Type Code';
            TableRelation = "House Type Master"."House Type Code";
            DataClassification = CustomerContent;
        }
        field(50105; "Plot-Specific Quantity"; Decimal)
        {
            Caption = 'Plot-Specific Quantity';
            DecimalPlaces = 0 : 5;
            DataClassification = CustomerContent;

            trigger OnValidate()
            begin
                if "Plot-Specific Quantity" < 0 then
                    Error('Plot-Specific Quantity cannot be negative.');
                
                // Update main quantity if this is a plot-based line
                if "Plot Code" <> '' then
                    Quantity := "Plot-Specific Quantity";
            end;
        }
        field(50106; "Delivery Location Code"; Code[20])
        {
            Caption = 'Delivery Location Code';
            TableRelation = "Delivery Locations"."Location Code" where("Plot Code" = field("Plot Code"));
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                DeliveryLocations: Record "Delivery Locations";
            begin
                if "Delivery Location Code" <> '' then begin
                    DeliveryLocations.Get("Delivery Location Code", "Plot Code");
                    "Delivery Location Name" := DeliveryLocations."Location Name";
                    "Delivery Address" := DeliveryLocations."Address";
                    "Delivery Contact" := DeliveryLocations."Contact Name";
                end else begin
                    "Delivery Location Name" := '';
                    "Delivery Address" := '';
                    "Delivery Contact" := '';
                end;
            end;
        }
        field(50107; "Delivery Location Name"; Text[100])
        {
            Caption = 'Delivery Location Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50108; "Delivery Address"; Text[100])
        {
            Caption = 'Delivery Address';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50109; "Delivery Contact"; Text[100])
        {
            Caption = 'Delivery Contact';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50110; "Planned Delivery Date"; Date)
        {
            Caption = 'Planned Delivery Date';
            DataClassification = CustomerContent;
        }
        field(50111; "Installation Phase"; Text[50])
        {
            Caption = 'Installation Phase';
            DataClassification = CustomerContent;
        }
        field(50112; "Material Category"; Text[50])
        {
            Caption = 'Material Category';
            DataClassification = CustomerContent;
        }
        field(50113; "Plot Line Status"; Enum "Plot Line Status")
        {
            Caption = 'Plot Line Status';
            DataClassification = CustomerContent;
        }
        field(50114; "Allocated Quantity"; Decimal)
        {
            Caption = 'Allocated Quantity';
            DecimalPlaces = 0 : 5;
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50115; "Remaining Quantity"; Decimal)
        {
            Caption = 'Remaining Quantity';
            DecimalPlaces = 0 : 5;
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(50116; "Line Notes"; Text[250])
        {
            Caption = 'Line Notes';
            DataClassification = CustomerContent;
        }
    }
}
