page 50123 "Plot Blanket Order List"
{
    ApplicationArea = All;
    Caption = 'Plot Blanket Orders';
    PageType = List;
    SourceTable = "Plot Blanket Order";
    UsageCategory = Lists;
    CardPageId = "Plot Blanket Order Card";
    Editable = false;

    layout
    {
        area(content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot blanket order number.';
                }
                field("Sell-to Customer No."; Rec."Sell-to Customer No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer number.';
                }
                field("Sell-to Customer Name"; Rec."Sell-to Customer Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer name.';
                }
                field("Site Code"; Rec."Site Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site code.';
                }
                field("Site Name"; Rec."Site Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site name.';
                }
                field("Order Date"; Rec."Order Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the order date.';
                }
                field("Status"; Rec."Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the order status.';
                }
                field("Expected Start Date"; Rec."Expected Start Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected start date.';
                }
                field("Expected Completion Date"; Rec."Expected Completion Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected completion date.';
                }
                field("Total Plots"; Rec."Total Plots")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the total number of plots.';
                }
                field("Active Plots"; Rec."Active Plots")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the number of active plots.';
                }
                field("Primary Contact Name"; Rec."Primary Contact Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the primary contact name.';
                }
            }
        }
        area(factboxes)
        {
            part("Site Information"; "Site Information FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "Site Code" = field("Site Code");
            }
            part("Customer Details"; "Plot Customer Details FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "No." = field("Sell-to Customer No.");
            }
        }
    }

    actions
    {
        area(processing)
        {
            action("New Plot Blanket Order")
            {
                ApplicationArea = All;
                Caption = 'New Plot Blanket Order';
                Image = New;
                RunObject = Page "Plot Blanket Order Card";
                RunPageMode = Create;
                ToolTip = 'Create a new plot blanket order.';
            }
        }
        area(navigation)
        {
            group("Order")
            {
                Caption = 'Order';
                action("Plot Lines")
                {
                    ApplicationArea = All;
                    Caption = 'Plot Lines';
                    Image = Line;
                    RunObject = Page "Plot Blanket Order Lines";
                    RunPageLink = "Document No." = field("No.");
                    ToolTip = 'View or edit plot lines for this order.';
                }
                action("Site Card")
                {
                    ApplicationArea = All;
                    Caption = 'Site Card';
                    Image = Home;
                    RunObject = Page "Site Card";
                    RunPageLink = "Site Code" = field("Site Code");
                    ToolTip = 'View the site card.';
                }
                action("Customer Card")
                {
                    ApplicationArea = All;
                    Caption = 'Customer Card';
                    Image = Customer;
                    RunObject = Page "Customer Card";
                    RunPageLink = "No." = field("Sell-to Customer No.");
                    ToolTip = 'View the customer card.';
                }
            }
        }
        area(reporting)
        {
            action("Plot Order Summary")
            {
                ApplicationArea = All;
                Caption = 'Plot Order Summary';
                Image = Report;
                ToolTip = 'Print a summary report for the plot order.';

                trigger OnAction()
                begin
                    Message('Plot Order Summary report will be implemented in Phase 3.');
                end;
            }
        }
    }
}
