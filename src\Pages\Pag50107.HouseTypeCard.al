page 50107 "House Type Card"
{
    ApplicationArea = All;
    Caption = 'House Type Card';
    PageType = Card;
    SourceTable = "House Type Master";

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("House Type Code"; Rec."House Type Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the unique code for the house type.';
                }
                field("House Type Name"; Rec."House Type Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the name of the house type.';
                }
                field("Category"; Rec."Category")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the category of the house type.';
                }
                field("Status"; Rec."Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the current status of the house type.';
                }
            }
            group("Description Details")
            {
                Caption = 'Description';
                field("Description"; Rec."Description")
                {
                    ApplicationArea = All;
                    MultiLine = true;
                    ToolTip = 'Specifies a description of the house type.';
                }
            }
            group("Specifications")
            {
                Caption = 'Specifications';
                field("Floor Area (sq ft)"; Rec."Floor Area (sq ft)")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the floor area in square feet.';
                }
                field("No. of Bedrooms"; Rec."No. of Bedrooms")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the number of bedrooms.';
                }
                field("No. of Bathrooms"; Rec."No. of Bathrooms")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the number of bathrooms.';
                }
                field("Garage"; Rec."Garage")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if the house type includes a garage.';
                }
                field("Garden"; Rec."Garden")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if the house type includes a garden.';
                }
                field("Estimated Build Time (Days)"; Rec."Estimated Build Time (Days)")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the estimated build time in days.';
                }
            }
            group("Planning Information")
            {
                Caption = 'Planning Information';
                field("Architect"; Rec."Architect")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the architect for this house type.';
                }
                field("Planning Reference"; Rec."Planning Reference")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the planning reference number.';
                }
            }
            group("Additional Information")
            {
                Caption = 'Additional Information';
                field("Special Requirements"; Rec."Special Requirements")
                {
                    ApplicationArea = All;
                    MultiLine = true;
                    ToolTip = 'Specifies any special requirements for the house type.';
                }
            }
            group("Statistics")
            {
                Caption = 'Statistics';
                field("Total Material Lines"; Rec."Total Material Lines")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the total number of material lines.';
                }
                field("Total Material Cost"; Rec."Total Material Cost")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the total material cost.';
                }
            }
            group("Audit Information")
            {
                Caption = 'Audit Information';
                field("Created Date"; Rec."Created Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies when the house type was created.';
                }
                field("Created By"; Rec."Created By")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies who created the house type.';
                }
                field("Last Modified Date"; Rec."Last Modified Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies when the house type was last modified.';
                }
                field("Last Modified By"; Rec."Last Modified By")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies who last modified the house type.';
                }
            }
        }
        area(factboxes)
        {
            systempart(Control1900383207; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(Control1905767507; Notes)
            {
                ApplicationArea = Notes;
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("House Type")
            {
                Caption = 'House Type';
                action("Materials")
                {
                    ApplicationArea = All;
                    Caption = 'Materials';
                    Image = ItemLedger;
                    RunObject = Page "House Type Materials";
                    RunPageLink = "House Type Code" = field("House Type Code");
                    ToolTip = 'View or edit materials for this house type.';
                }
                action("Plots")
                {
                    ApplicationArea = All;
                    Caption = 'Plots';
                    Image = Planning;
                    RunObject = Page "Plot List";
                    RunPageLink = "House Type Code" = field("House Type Code");
                    ToolTip = 'View plots using this house type.';
                }
            }
        }
        area(processing)
        {
            action("Copy House Type")
            {
                ApplicationArea = All;
                Caption = 'Copy House Type';
                Image = Copy;
                ToolTip = 'Copy this house type to create a new one.';

                trigger OnAction()
                var
                    HouseTypeMaterials: Record "House Type Materials";
                    NewHouseTypeMaster: Record "House Type Master";
                    NewHouseTypeMaterials: Record "House Type Materials";
                    NewHouseTypeCode: Code[20];
                begin
                    NewHouseTypeCode := Rec."House Type Code" + '-COPY';
                    if NewHouseTypeMaster.Get(NewHouseTypeCode) then
                        Error('House type %1 already exists.', NewHouseTypeCode);

                    // Copy house type master
                    NewHouseTypeMaster := Rec;
                    NewHouseTypeMaster."House Type Code" := NewHouseTypeCode;
                    NewHouseTypeMaster."House Type Name" := Rec."House Type Name" + ' (Copy)';
                    NewHouseTypeMaster.Insert(true);

                    // Copy materials
                    HouseTypeMaterials.SetRange("House Type Code", Rec."House Type Code");
                    if HouseTypeMaterials.FindSet() then
                        repeat
                            NewHouseTypeMaterials := HouseTypeMaterials;
                            NewHouseTypeMaterials."House Type Code" := NewHouseTypeCode;
                            NewHouseTypeMaterials.Insert(true);
                        until HouseTypeMaterials.Next() = 0;

                    Message('House type %1 has been copied to %2.', Rec."House Type Code", NewHouseTypeCode);
                end;
            }
        }
    }
}
