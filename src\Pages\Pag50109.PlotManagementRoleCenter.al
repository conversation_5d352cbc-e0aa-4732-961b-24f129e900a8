page 50109 "Plot Management Role Center"
{
    Caption = 'Plot Management';
    PageType = RoleCenter;

    layout
    {
        area(rolecenter)
        {
            group(Control1900724808)
            {
                ShowCaption = false;
                part(Control1900724708; "Headline RC Business Manager")
                {
                    ApplicationArea = Basic, Suite;
                }
                part("User Tasks Activities"; "User Tasks Activities")
                {
                    ApplicationArea = Suite;
                }
            }
            group(Control1900724708)
            {
                ShowCaption = false;
                systempart(Control1901377608; MyNotes)
                {
                    ApplicationArea = Basic, Suite;
                }
            }
        }
    }

    actions
    {
        area(reporting)
        {
            action("Site Summary Report")
            {
                ApplicationArea = All;
                Caption = 'Site Summary Report';
                Image = Report;
                ToolTip = 'Generate a summary report for sites.';
                
                trigger OnAction()
                begin
                    Message('Site Summary report will be implemented in Phase 8.');
                end;
            }
            action("Plot Status Report")
            {
                ApplicationArea = All;
                Caption = 'Plot Status Report';
                Image = Report;
                ToolTip = 'Generate a status report for plots.';
                
                trigger OnAction()
                begin
                    Message('Plot Status report will be implemented in Phase 8.');
                end;
            }
        }
        area(embedding)
        {
            action("Sites")
            {
                ApplicationArea = All;
                Caption = 'Sites';
                Image = Home;
                RunObject = Page "Site List";
                ToolTip = 'View and manage sites.';
            }
            action("Phases")
            {
                ApplicationArea = All;
                Caption = 'Phases';
                Image = Planning;
                RunObject = Page "Phase List";
                ToolTip = 'View and manage phases.';
            }
            action("Plots")
            {
                ApplicationArea = All;
                Caption = 'Plots';
                Image = ItemLedger;
                RunObject = Page "Plot List";
                ToolTip = 'View and manage plots.';
            }
            action("House Types")
            {
                ApplicationArea = All;
                Caption = 'House Types';
                Image = Item;
                RunObject = Page "House Type List";
                ToolTip = 'View and manage house types.';
            }
        }
        area(sections)
        {
            group("Site Management")
            {
                Caption = 'Site Management';
                Image = Home;
                action("Site List")
                {
                    ApplicationArea = All;
                    Caption = 'Sites';
                    RunObject = Page "Site List";
                    ToolTip = 'View and manage sites.';
                }
                action("Phase List")
                {
                    ApplicationArea = All;
                    Caption = 'Phases';
                    RunObject = Page "Phase List";
                    ToolTip = 'View and manage phases.';
                }
                action("Plot List")
                {
                    ApplicationArea = All;
                    Caption = 'Plots';
                    RunObject = Page "Plot List";
                    ToolTip = 'View and manage plots.';
                }
            }
            group("House Type Management")
            {
                Caption = 'House Type Management';
                Image = Item;
                action("House Type List")
                {
                    ApplicationArea = All;
                    Caption = 'House Types';
                    RunObject = Page "House Type List";
                    ToolTip = 'View and manage house types.';
                }
            }
            group("Standard BC")
            {
                Caption = 'Standard Business Central';
                Image = Administration;
                action("Customers")
                {
                    ApplicationArea = All;
                    Caption = 'Customers';
                    RunObject = Page "Customer List";
                    ToolTip = 'View and manage customers.';
                }
                action("Items")
                {
                    ApplicationArea = All;
                    Caption = 'Items';
                    RunObject = Page "Item List";
                    ToolTip = 'View and manage items.';
                }
                action("Vendors")
                {
                    ApplicationArea = All;
                    Caption = 'Vendors';
                    RunObject = Page "Vendor List";
                    ToolTip = 'View and manage vendors.';
                }
            }
        }
        area(creation)
        {
            action("New Site")
            {
                ApplicationArea = All;
                Caption = 'Site';
                Image = New;
                RunObject = Page "Site Card";
                RunPageMode = Create;
                ToolTip = 'Create a new site.';
            }
            action("New Phase")
            {
                ApplicationArea = All;
                Caption = 'Phase';
                Image = New;
                RunObject = Page "Phase Card";
                RunPageMode = Create;
                ToolTip = 'Create a new phase.';
            }
            action("New Plot")
            {
                ApplicationArea = All;
                Caption = 'Plot';
                Image = New;
                RunObject = Page "Plot Card";
                RunPageMode = Create;
                ToolTip = 'Create a new plot.';
            }
            action("New House Type")
            {
                ApplicationArea = All;
                Caption = 'House Type';
                Image = New;
                RunObject = Page "House Type Card";
                RunPageMode = Create;
                ToolTip = 'Create a new house type.';
            }
        }
    }
}
