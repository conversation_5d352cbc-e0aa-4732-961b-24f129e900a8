page 50101 "Site Card"
{
    ApplicationArea = All;
    Caption = 'Site Card';
    PageType = Card;
    SourceTable = "Site Master";

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Site Code"; Rec."Site Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the unique code for the site.';
                }
                field("Site Name"; Rec."Site Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the name of the site.';
                }
                field("Customer No."; Rec."Customer No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer number for this site.';
                }
                field("Customer Name"; Rec."Customer Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer name for this site.';
                }
                field("Status"; Rec."Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the current status of the site.';
                }
            }
            group("Address")
            {
                Caption = 'Address';
                field("Site Address"; Rec."Site Address")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the address of the site.';
                }
                field("Site Address 2"; Rec."Site Address 2")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies additional address information.';
                }
                field("City"; Rec."City")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the city where the site is located.';
                }
                field("Post Code"; Rec."Post Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the postal code for the site.';
                }
                field("County"; Rec."County")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the county where the site is located.';
                }
                field("Country/Region Code"; Rec."Country/Region Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the country/region code for the site.';
                }
            }
            group("Contact Information")
            {
                Caption = 'Contact Information';
                field("Site Manager Name"; Rec."Site Manager Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the name of the site manager.';
                }
                field("Site Manager Phone"; Rec."Site Manager Phone")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phone number of the site manager.';
                }
                field("Site Manager Email"; Rec."Site Manager Email")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the email address of the site manager.';
                }
            }
            group("Project Dates")
            {
                Caption = 'Project Dates';
                field("Project Start Date"; Rec."Project Start Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the planned start date for the project.';
                }
                field("Expected Completion Date"; Rec."Expected Completion Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected completion date for the project.';
                }
            }
            group("Additional Information")
            {
                Caption = 'Additional Information';
                field("Access Instructions"; Rec."Access Instructions")
                {
                    ApplicationArea = All;
                    MultiLine = true;
                    ToolTip = 'Specifies special instructions for accessing the site.';
                }
                field("Special Requirements"; Rec."Special Requirements")
                {
                    ApplicationArea = All;
                    MultiLine = true;
                    ToolTip = 'Specifies any special requirements for the site.';
                }
            }
            group("Statistics")
            {
                Caption = 'Statistics';
                field("Total Plots"; Rec."Total Plots")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the total number of plots in this site.';
                }
                field("Active Plots"; Rec."Active Plots")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the number of active plots in this site.';
                }
            }
        }
        area(factboxes)
        {
            systempart(Control1900383207; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(Control1905767507; Notes)
            {
                ApplicationArea = Notes;
            }
        }
    }

    actions
    {
        area(navigation)
        {
            group("Site")
            {
                Caption = 'Site';
                action("Phases")
                {
                    ApplicationArea = All;
                    Caption = 'Phases';
                    Image = Planning;
                    RunObject = Page "Phase List";
                    RunPageLink = "Site Code" = field("Site Code");
                    ToolTip = 'View or edit phases for this site.';
                }
                action("Plots")
                {
                    ApplicationArea = All;
                    Caption = 'Plots';
                    Image = ItemLedger;
                    RunObject = Page "Plot List";
                    RunPageLink = "Site Code" = field("Site Code");
                    ToolTip = 'View or edit plots for this site.';
                }
            }
        }
        area(processing)
        {
            action("Create Phase")
            {
                ApplicationArea = All;
                Caption = 'Create Phase';
                Image = NewDocument;
                ToolTip = 'Create a new phase for this site.';
                
                trigger OnAction()
                var
                    PhaseMaster: Record "Phase Master";
                    PhaseCard: Page "Phase Card";
                begin
                    PhaseMaster.Init();
                    PhaseMaster."Site Code" := Rec."Site Code";
                    PhaseMaster.Insert(true);
                    PhaseCard.SetRecord(PhaseMaster);
                    PhaseCard.Run();
                end;
            }
        }
    }
}
