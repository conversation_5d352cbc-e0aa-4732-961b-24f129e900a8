# Product Requirements Document
## Business Central Enhanced Bulk Sales Orders for Brick Trading

**Version:** 1.0  
**Date:** June 16, 2025  
**Document Owner:** [Product Manager]  
**Stakeholders:** Sales, Operations, Finance, IT Development

---

## 1. Executive Summary

### 1.1 Product Vision
Transform Business Central's standard sales order functionality to support complex brick trading operations with blanket orders, plot-based scheduling, stock allocation, and call-off management for major housebuilders.

### 1.2 Business Objectives
- **Reduce order processing time** by 60% through automated call-off generation
- **Improve delivery accuracy** to 98%+ through enhanced scheduling
- **Increase customer satisfaction** via better delivery predictability
- **Optimize stock allocation** across multiple sites and plots

---

## 2. Problem Statement

### 2.1 Current Challenges
- **Manual scheduling process** for multi-million pound housing site contracts
- **No plot-level tracking** in standard Business Central
- **Inefficient stock allocation** across multiple delivery locations
- **Complex call-off management** requiring custom workarounds
- **Limited visibility** into delivery commitments vs. actual performance

### 2.2 Business Impact
- Lost revenue due to delivery delays and stock misallocation
- Increased administrative overhead
- Customer complaints about delivery accuracy
- Poor cash flow visibility

---

## 3. Solution Overview

### 3.1 Core Components
1. **Enhanced Blanket Sales Orders**
2. **Plot Management System**
3. **Advanced Scheduling Engine**
4. **Stock Allocation Management**
5. **Automated Call-off Generation**

### 3.2 Technical Architecture
- **Frontend:** Business Central Role Centers and Custom Pages
- **Backend:** Custom AL Extensions, Tables, and Codeunits
- **Integration:** Standard BC APIs for inventory and sales

---

## 4. Functional Requirements

### 4.1 Blanket Order Management

#### 4.1.1 Enhanced Blanket Orders
**Requirements:**
- Support for multiple delivery locations per blanket order
- Plot-based line allocation with individual delivery schedules
- Customer contact management by plot/phase
- Commitment tracking with variance analysis
- Credit limit validation at call-off level

**Acceptance Criteria:**
- Users can create blanket orders with 50+ plots per site
- Each plot can have different delivery schedules and contacts
- System validates credit limits before generating call-offs
- Variance reporting shows expected vs. actual deliveries by month

### 4.2 Plot Management System

#### 4.2.1 Plot Master Data
**Requirements:**
- Plot/Phase master data with hierarchical structure
- Customer and contact assignment per plot
- Delivery address and access instructions
- Phase dependencies and delivery sequences
- Plot status tracking (Active, On Hold, Completed, Cancelled)

**Data Structure:**
```
Plot Master:
- Plot Code (Primary Key)
- Site Code (FK to Customer/Site)
- Customer Code
- Plot Address
- Contact Name/Phone/Email
- Expected Start Date
- Expected Completion Date
- Phase/Block Reference
- Access Instructions
- Special Requirements
- Status
```

#### 4.2.2 Site Hierarchy
**Requirements:**
- Customer → Site → Phase → Plot hierarchy
- Bulk operations at site/phase level
- Inherited settings with plot-level overrides

### 4.3 Advanced Scheduling Engine

#### 4.3.1 Schedule Management
**Requirements:**
- Monthly/weekly commitment views
- Conflict detection and resolution
- Capacity planning integration
- Weather/seasonal adjustments

**Features:**
- **Schedule Dashboard:** Monthly view showing Expected, Scheduled, Delivered quantities
- **Conflict Alerts:** Automatic detection of over-committed periods
- **Capacity Checks:** Integration with vehicle/delivery capacity

#### 4.3.2 Allocation Logic
**Requirements:**
- Forward allocation of stock to scheduled deliveries
- Multi-location stock optimization
- Priority-based allocation rules
- Shortage management workflows

### 4.4 Stock Allocation Management

#### 4.4.1 Reservation System
**Requirements:**
- Stock reservation tied to delivery schedules
- Multi-location allocation optimization
- Automated reallocation on schedule changes
- Available-to-Promise (ATP) calculations

**Features:**
- **Reserved Stock Tracking:** Real-time view of committed inventory
- **Location Optimization:** Suggest optimal allocation across warehouses
- **Reallocation Automation:** Auto-adjust when schedules change
- **ATP Integration:** Real-time availability checking

### 4.5 Call-off Generation

#### 4.5.1 Automated Call-offs
**Requirements:**
- Scheduled call-off generation from blanket orders
- Plot-specific sales order creation
- Delivery documentation with plot references
- Integration with existing delivery workflow

**Process Flow:**
1. System identifies due call-offs based on schedule
2. Validates stock availability and credit limits
3. Creates sales order with plot-specific details
4. Generates delivery documentation
5. Updates blanket order commitments

### 4.6 Supplier Management & Communication

#### 4.6.1 Supplier Integration
**Requirements:**
- Automated PDF schedule generation for suppliers
- Email delivery of schedules to suppliers
- Supplier confirmation and acknowledgment tracking
- Factory-based allocation management per supplier

**Features:**
- **Schedule PDF Generation:** Automated creation of supplier-specific schedules
- **Factory Allocation Tracking:** Monitor allocations by supplier factory with limits
- **Supplier Communication Log:** Track all communications and confirmations

#### 4.6.2 Factory Allocation Management
**Requirements:**
- Factory master data with allocation limits
- Real-time tracking of allocated vs. available capacity
- Over-allocation prevention and warning system
- Allocation optimization across multiple factories

### 4.7 Bespoke Order Management

#### 4.7.1 Bespoke Product Handling
**Requirements:**
- Bespoke product identification and flagging
- Approval workflow for custom orders
- Special documentation and liability tracking
- Enhanced pricing and costing for bespoke items

**Process Flow:**
1. Identify bespoke products during order entry
2. Route to approval workflow with documentation
3. Special pricing and costing validation
4. Enhanced tracking and delivery management

### 4.8 House Type Management

#### 4.8.1 House Type Master Data
**Requirements:**
- House type templates with material requirements
- Plot assignment to house types
- Change management for type modifications
- Impact analysis for type changes

**Features:**
- **Type Templates:** Predefined material lists per house type
- **Change Tracking:** Full audit trail of house type changes
- **Impact Analysis:** Automatic calculation of schedule/allocation impacts
- **Bulk Updates:** Efficient processing of multiple plot type changes