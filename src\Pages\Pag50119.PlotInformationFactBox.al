page 50119 "Plot Information FactBox"
{
    Caption = 'Plot Information';
    PageType = CardPart;
    SourceTable = "Plot Master";

    layout
    {
        area(content)
        {
            group(Control1)
            {
                ShowCaption = false;
                field("Plot Code"; Rec."Plot Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot code.';
                }
                field("Plot Number"; Rec."Plot Number")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot number.';
                }
                field("Site Code"; Rec."Site Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site code.';
                }
                field("Phase Code"; Rec."Phase Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phase code.';
                }
                field("House Type Code"; Rec."House Type Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the house type code.';
                }
                field("Status"; Rec."Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot status.';
                }
                field("Expected Start Date"; Rec."Expected Start Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected start date.';
                }
                field("Expected Completion Date"; Rec."Expected Completion Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected completion date.';
                }
                field("Plot Address"; Rec."Plot Address")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot address.';
                }
            }
        }
    }
}
