page 50113 "Delivery Location Card"
{
    Caption = 'Delivery Location Card';
    PageType = Card;
    SourceTable = "Delivery Locations";

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Location Code"; Rec."Location Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the location code.';
                }
                field("Plot Code"; Rec."Plot Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot code.';
                }
                field("Site Code"; Rec."Site Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site code.';
                }
                field("Phase Code"; Rec."Phase Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phase code.';
                }
                field("Location Name"; Rec."Location Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the location name.';
                }
                field("Location Type"; Rec."Location Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the location type.';
                }
                field("Primary Location"; Rec."Primary Location")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if this is the primary location.';
                }
                field("Active"; Rec."Active")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if the location is active.';
                }
            }
            group("Address Information")
            {
                Caption = 'Address Information';
                field("Address"; Rec."Address")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the address.';
                }
                field("Address 2"; Rec."Address 2")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies additional address information.';
                }
                field("City"; Rec."City")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the city.';
                }
                field("Post Code"; Rec."Post Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the post code.';
                }
                field("County"; Rec."County")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the county.';
                }
                field("Country/Region Code"; Rec."Country/Region Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the country/region code.';
                }
                field("GPS Coordinates"; Rec."GPS Coordinates")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the GPS coordinates.';
                }
            }
            group("Contact Information")
            {
                Caption = 'Contact Information';
                field("Contact Name"; Rec."Contact Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact name.';
                }
                field("Phone No."; Rec."Phone No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phone number.';
                }
                field("E-Mail"; Rec."E-Mail")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the email address.';
                }
            }
            group("Delivery Information")
            {
                Caption = 'Delivery Information';
                field("Access Instructions"; Rec."Access Instructions")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies access instructions.';
                    MultiLine = true;
                }
                field("Delivery Hours"; Rec."Delivery Hours")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies delivery hours.';
                }
                field("Special Requirements"; Rec."Special Requirements")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies special requirements.';
                    MultiLine = true;
                }
            }
        }
        area(factboxes)
        {
            part("Plot Information"; "Plot Information FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "Plot Code" = field("Plot Code");
            }
        }
    }

    actions
    {
        area(navigation)
        {
            action("Plot Card")
            {
                ApplicationArea = All;
                Caption = 'Plot Card';
                Image = Card;
                ToolTip = 'View the plot card.';
                
                trigger OnAction()
                var
                    PlotCard: Page "Plot Card";
                begin
                    PlotCard.SetPlotCode(Rec."Plot Code");
                    PlotCard.Run();
                end;
            }
            action("Site Card")
            {
                ApplicationArea = All;
                Caption = 'Site Card';
                Image = Home;
                ToolTip = 'View the site card.';
                
                trigger OnAction()
                var
                    SiteCard: Page "Site Card";
                begin
                    SiteCard.SetSiteCode(Rec."Site Code");
                    SiteCard.Run();
                end;
            }
        }
        area(processing)
        {
            action("Set as Primary")
            {
                ApplicationArea = All;
                Caption = 'Set as Primary';
                Image = Default;
                ToolTip = 'Set this location as the primary delivery location for the plot.';
                
                trigger OnAction()
                begin
                    Rec."Primary Location" := true;
                    Rec.Modify();
                    Message('Location %1 set as primary for plot %2.', Rec."Location Code", Rec."Plot Code");
                end;
            }
        }
    }
}
