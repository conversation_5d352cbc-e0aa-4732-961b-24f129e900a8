table 50100 "Site Master"
{
    Caption = 'Site Master';
    DataClassification = CustomerContent;
    LookupPageId = "Site List";
    DrillDownPageId = "Site List";

    fields
    {
        field(1; "Site Code"; Code[20])
        {
            Caption = 'Site Code';
            NotBlank = true;
            DataClassification = CustomerContent;
        }
        field(2; "Site Name"; Text[100])
        {
            Caption = 'Site Name';
            DataClassification = CustomerContent;
        }
        field(3; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = Customer."No.";
            DataClassification = CustomerContent;

            trigger OnValidate()
            var
                Customer: Record Customer;
            begin
                if "Customer No." <> '' then begin
                    Customer.Get("Customer No.");
                    "Customer Name" := Customer.Name;
                end else
                    "Customer Name" := '';
            end;
        }
        field(4; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            DataClassification = CustomerContent;
        }
        field(5; "Site Address"; Text[100])
        {
            Caption = 'Site Address';
            DataClassification = CustomerContent;
        }
        field(6; "Site Address 2"; Text[50])
        {
            Caption = 'Site Address 2';
            DataClassification = CustomerContent;
        }
        field(7; "City"; Text[30])
        {
            Caption = 'City';
            DataClassification = CustomerContent;
        }
        field(8; "Post Code"; Code[20])
        {
            Caption = 'Post Code';
            DataClassification = CustomerContent;
        }
        field(9; "County"; Text[30])
        {
            Caption = 'County';
            DataClassification = CustomerContent;
        }
        field(10; "Country/Region Code"; Code[10])
        {
            Caption = 'Country/Region Code';
            TableRelation = "Country/Region";
            DataClassification = CustomerContent;
        }
        field(11; "Site Manager Name"; Text[50])
        {
            Caption = 'Site Manager Name';
            DataClassification = CustomerContent;
        }
        field(12; "Site Manager Phone"; Text[30])
        {
            Caption = 'Site Manager Phone';
            DataClassification = CustomerContent;
        }
        field(13; "Site Manager Email"; Text[80])
        {
            Caption = 'Site Manager Email';
            DataClassification = CustomerContent;
        }
        field(14; "Project Start Date"; Date)
        {
            Caption = 'Project Start Date';
            DataClassification = CustomerContent;
        }
        field(15; "Expected Completion Date"; Date)
        {
            Caption = 'Expected Completion Date';
            DataClassification = CustomerContent;
        }
        field(16; "Status"; Enum "Site Status")
        {
            Caption = 'Status';
            DataClassification = CustomerContent;
        }
        field(17; "Access Instructions"; Text[250])
        {
            Caption = 'Access Instructions';
            DataClassification = CustomerContent;
        }
        field(18; "Special Requirements"; Text[250])
        {
            Caption = 'Special Requirements';
            DataClassification = CustomerContent;
        }
        field(19; "Total Plots"; Integer)
        {
            Caption = 'Total Plots';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Plot Master" where("Site Code" = field("Site Code")));
        }
        field(20; "Active Plots"; Integer)
        {
            Caption = 'Active Plots';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Plot Master" where("Site Code" = field("Site Code"), Status = const(Active)));
        }
        field(21; "Total Phases"; Integer)
        {
            Caption = 'Total Phases';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Phase Master" where("Site Code" = field("Site Code")));
        }
    }

    keys
    {
        key(PK; "Site Code")
        {
            Clustered = true;
        }
        key(Customer; "Customer No.", "Site Code")
        {
        }
    }

    trigger OnDelete()
    var
        PhaseMaster: Record "Phase Master";
        PlotMaster: Record "Plot Master";
    begin
        // Check for related phases
        PhaseMaster.SetRange("Site Code", "Site Code");
        if not PhaseMaster.IsEmpty() then
            Error('Cannot delete site %1 because it has related phases.', "Site Code");

        // Check for related plots
        PlotMaster.SetRange("Site Code", "Site Code");
        if not PlotMaster.IsEmpty() then
            Error('Cannot delete site %1 because it has related plots.', "Site Code");
    end;
}
