page 50112 "Delivery Locations List"
{
    Caption = 'Delivery Locations';
    PageType = List;
    SourceTable = "Delivery Locations";
    UsageCategory = Lists;
    ApplicationArea = All;
    CardPageId = "Delivery Location Card";

    layout
    {
        area(content)
        {
            repeater(Control1)
            {
                field("Location Code"; Rec."Location Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the location code.';
                }
                field("Plot Code"; Rec."Plot Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot code.';
                }
                field("Site Code"; Rec."Site Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site code.';
                }
                field("Location Name"; Rec."Location Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the location name.';
                }
                field("Location Type"; Rec."Location Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the location type.';
                }
                field("Address"; Rec."Address")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the address.';
                }
                field("City"; Rec."City")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the city.';
                }
                field("Post Code"; Rec."Post Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the post code.';
                }
                field("Contact Name"; Rec."Contact Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact name.';
                }
                field("Phone No."; Rec."Phone No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phone number.';
                }
                field("Primary Location"; Rec."Primary Location")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if this is the primary location.';
                }
                field("Active"; Rec."Active")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if the location is active.';
                }
            }
        }
        area(factboxes)
        {
            part("Plot Information"; "Plot Information FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "Plot Code" = field("Plot Code");
            }
        }
    }

    actions
    {
        area(processing)
        {
            action("Create Default Locations")
            {
                ApplicationArea = All;
                Caption = 'Create Default Locations';
                Image = NewDocument;
                ToolTip = 'Create default delivery locations for the selected plot.';
                
                trigger OnAction()
                var
                    BlanketOrderManagement: Codeunit "Blanket Order Management";
                begin
                    if Rec."Plot Code" <> '' then begin
                        BlanketOrderManagement.CreateDefaultDeliveryLocations(Rec."Plot Code");
                        CurrPage.Update();
                        Message('Default delivery locations created for plot %1.', Rec."Plot Code");
                    end else
                        Error('Please select a plot first.');
                end;
            }
            action("Set as Primary")
            {
                ApplicationArea = All;
                Caption = 'Set as Primary';
                Image = Default;
                ToolTip = 'Set this location as the primary delivery location for the plot.';
                
                trigger OnAction()
                begin
                    Rec."Primary Location" := true;
                    Rec.Modify();
                    CurrPage.Update();
                end;
            }
        }
        area(navigation)
        {
            action("Plot Card")
            {
                ApplicationArea = All;
                Caption = 'Plot Card';
                Image = Card;
                ToolTip = 'View the plot card.';
                
                trigger OnAction()
                var
                    PlotCard: Page "Plot Card";
                begin
                    PlotCard.SetPlotCode(Rec."Plot Code");
                    PlotCard.Run();
                end;
            }
        }
    }
}
