page 50122 "Plot Grid View"
{
    ApplicationArea = All;
    Caption = 'Plot Grid View';
    PageType = List;
    SourceTable = "Plot Master";
    Editable = false;

    layout
    {
        area(content)
        {
            repeater(Grid)
            {
                field("Plot Code"; Rec."Plot Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the unique code for the plot.';
                }
                field("Plot Number"; Rec."Plot Number")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot number.';
                }
                field("Site Code"; Rec."Site Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site code.';
                }
                field("Phase Code"; Rec."Phase Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phase code.';
                }
                field("House Type Code"; Rec."House Type Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the house type code.';
                }
                field("Status"; Rec."Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot status.';
                }
                field("Expected Start Date"; Rec."Expected Start Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected start date.';
                }
                field("Expected Completion Date"; Rec."Expected Completion Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the expected completion date.';
                }
                field("Plot Address"; Rec."Plot Address")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot address.';
                }
                field("Customer Name"; Rec."Customer Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the customer name.';
                }
            }
        }
        area(factboxes)
        {
            part("Plot Information"; "Plot Information FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "Plot Code" = field("Plot Code");
            }
        }
    }

    actions
    {
        area(processing)
        {
            action("Plot Card")
            {
                ApplicationArea = All;
                Caption = 'Plot Card';
                Image = Card;
                ToolTip = 'View the plot card.';

                trigger OnAction()
                var
                    PlotCard: Page "Plot Card";
                begin
                    PlotCard.SetPlotCode(Rec."Plot Code");
                    PlotCard.Run();
                end;
            }
        }
    }

    /// <summary>
    /// Sets a filter for a specific site
    /// </summary>
    procedure SetSiteFilter(SiteCode: Code[20])
    begin
        if SiteCode <> '' then begin
            Rec.SetRange("Site Code", SiteCode);
            CurrPage.Update();
        end;
    end;
}
