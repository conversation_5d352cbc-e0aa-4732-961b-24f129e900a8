page 50117 "Communication Log Card"
{
    Caption = 'Communication Log Card';
    PageType = Card;
    SourceTable = "Communication Log";

    layout
    {
        area(content)
        {
            group(General)
            {
                Caption = 'General';
                field("Entry No."; Rec."Entry No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the entry number.';
                    Editable = false;
                }
                field("Communication Date"; Rec."Communication Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the communication date.';
                }
                field("Communication Time"; Rec."Communication Time")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the communication time.';
                }
                field("Communication Type"; Rec."Communication Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the communication type.';
                }
                field("Communication Method"; Rec."Communication Method")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the communication method.';
                }
                field("Direction"; Rec."Direction")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the communication direction.';
                }
                field("Priority"; Rec."Priority")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the priority.';
                }
                field("Status"; Rec."Status")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the status.';
                }
            }
            group("Location Information")
            {
                Caption = 'Location Information';
                field("Site Code"; Rec."Site Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the site code.';
                }
                field("Phase Code"; Rec."Phase Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the phase code.';
                }
                field("Plot Code"; Rec."Plot Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the plot code.';
                }
            }
            group("Contact Information")
            {
                Caption = 'Contact Information';
                field("Contact Code"; Rec."Contact Code")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact code.';
                }
                field("Contact Name"; Rec."Contact Name")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact name.';
                    Editable = false;
                }
                field("Contact Type"; Rec."Contact Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the contact type.';
                    Editable = false;
                }
            }
            group("Communication Details")
            {
                Caption = 'Communication Details';
                field("Subject"; Rec."Subject")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the subject.';
                }
                field("Description"; Rec."Description")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the description.';
                    MultiLine = true;
                }
            }
            group("Follow-up Information")
            {
                Caption = 'Follow-up Information';
                field("Follow-up Required"; Rec."Follow-up Required")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if follow-up is required.';
                }
                field("Follow-up Date"; Rec."Follow-up Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the follow-up date.';
                    Enabled = Rec."Follow-up Required";
                }
                field("Follow-up Completed"; Rec."Follow-up Completed")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if follow-up is completed.';
                    Enabled = Rec."Follow-up Required";
                }
            }
            group("Related Document")
            {
                Caption = 'Related Document';
                field("Document Type"; Rec."Document Type")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the related document type.';
                }
                field("Document No."; Rec."Document No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the related document number.';
                }
                field("Document Line No."; Rec."Document Line No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the related document line number.';
                }
            }
            group("Attachment Information")
            {
                Caption = 'Attachment Information';
                field("Attachment"; Rec."Attachment")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies if there is an attachment.';
                }
                field("Attachment Path"; Rec."Attachment Path")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the attachment path.';
                    Enabled = Rec."Attachment";
                }
            }
            group("Audit Information")
            {
                Caption = 'Audit Information';
                field("Created By"; Rec."Created By")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies who created the entry.';
                    Editable = false;
                }
                field("Created Date"; Rec."Created Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies when the entry was created.';
                    Editable = false;
                }
                field("Last Modified By"; Rec."Last Modified By")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies who last modified the entry.';
                    Editable = false;
                }
                field("Last Modified Date"; Rec."Last Modified Date")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies when the entry was last modified.';
                    Editable = false;
                }
            }
        }
        area(factboxes)
        {
            part("Plot Information"; "Plot Information FactBox")
            {
                ApplicationArea = All;
                SubPageLink = "Plot Code" = field("Plot Code");
                Visible = Rec."Plot Code" <> '';
            }
        }
    }

    actions
    {
        area(processing)
        {
            action("Mark as Completed")
            {
                ApplicationArea = All;
                Caption = 'Mark as Completed';
                Image = Completed;
                ToolTip = 'Mark the communication as completed.';

                trigger OnAction()
                begin
                    Rec."Status" := Rec."Status"::Resolved;
                    if Rec."Follow-up Required" then
                        Rec."Follow-up Completed" := true;
                    Rec.Modify();
                    CurrPage.Update();
                    Message('Communication marked as completed.');
                end;
            }
            action("Create Follow-up Entry")
            {
                ApplicationArea = All;
                Caption = 'Create Follow-up Entry';
                Image = NewDocument;
                ToolTip = 'Create a new follow-up communication entry.';

                trigger OnAction()
                var
                    CommunicationLog: Record "Communication Log";
                    CommunicationLogCard: Page "Communication Log Card";
                begin
                    CommunicationLog.Init();
                    CommunicationLog."Site Code" := Rec."Site Code";
                    CommunicationLog."Phase Code" := Rec."Phase Code";
                    CommunicationLog."Plot Code" := Rec."Plot Code";
                    CommunicationLog."Contact Code" := Rec."Contact Code";
                    CommunicationLog."Communication Type" := Rec."Communication Type";
                    CommunicationLog."Subject" := 'Follow-up: ' + Rec."Subject";
                    CommunicationLog.Insert(true);

                    // Mark original as followed up
                    Rec."Follow-up Completed" := true;
                    Rec.Modify();

                    CommunicationLogCard.SetRecord(CommunicationLog);
                    CommunicationLogCard.Run();
                end;
            }
        }
        area(navigation)
        {
            action("Contact Card")
            {
                ApplicationArea = All;
                Caption = 'Contact Card';
                Image = ContactPerson;
                ToolTip = 'View the contact card.';
                Enabled = Rec."Contact Code" <> '';

                trigger OnAction()
                var
                    PlotContactCard: Page "Plot Contact Card";
                    PlotContacts: Record "Plot Contacts";
                begin
                    if PlotContacts.Get(Rec."Contact Code", Rec."Site Code", Rec."Phase Code", Rec."Plot Code") then begin
                        PlotContactCard.SetRecord(PlotContacts);
                        PlotContactCard.Run();
                    end;
                end;
            }
            action("View Related Document")
            {
                ApplicationArea = All;
                Caption = 'View Related Document';
                Image = Document;
                ToolTip = 'View the related document.';
                Enabled = Rec."Document No." <> '';

                trigger OnAction()
                var
                    SalesHeader: Record "Sales Header";
                    EnhancedBlanketSalesOrder: Page "Enhanced Blanket Sales Order";
                begin
                    if SalesHeader.Get(Rec."Document Type", Rec."Document No.") then begin
                        EnhancedBlanketSalesOrder.SetRecord(SalesHeader);
                        EnhancedBlanketSalesOrder.Run();
                    end;
                end;
            }
        }
    }
}
